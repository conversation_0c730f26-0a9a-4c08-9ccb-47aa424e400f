# Traefik v3 + acme-dns Wildcard SSL Setup

This guide walks you through setting up wildcard SSL certificates using Traefik v3 and acme-dns, eliminating the need for GoDaddy API access.

## 🎯 **How acme-dns Works**

Instead of using GoDaddy's API, acme-dns provides a minimal DNS server that:
1. **Runs on your server** and handles DNS challenges for Let's Encrypt
2. **Uses CNAME records** in GoDaddy pointing to your acme-dns server
3. **No API keys required** - just standard DNS record management
4. **Uses SQLite** - lightweight database, no PostgreSQL dependency

## 🌐 **Your Services After Setup**

- **N8N**: https://n8n.dev.arumai.ai
- **Flowise**: https://flowise.dev.arumai.ai
- **Open WebUI**: https://chat.dev.arumai.ai
- **Qdrant**: https://vector.dev.arumai.ai
- **Ollama**: https://ai.dev.arumai.ai
- **Traefik Dashboard**: https://traefik.dev.arumai.ai
- **acme-dns API**: https://auth.dev.arumai.ai

## 📋 **Prerequisites**

1. **Ubuntu VPS Server** with Docker and Docker Compose
2. **Domain**: `dev.arumai.ai` managed by GoDaddy
3. **Server IP Address**: Your VPS public IP

## 🚀 **Step-by-Step Setup**

### **Step 1: Configure Environment Variables**

1. Copy the example environment file:
   ```bash
   cp env.example .env
   ```

2. Edit `.env` with your actual values:
   ```bash
   # Database Configuration (for n8n)
   POSTGRES_USER=arumai
   POSTGRES_PASSWORD=your_secure_password_here  # Used only by n8n
   POSTGRES_DB=arumai_db

   # N8N Configuration  
   N8N_ENCRYPTION_KEY=your_n8n_encryption_key_here  # Generate: openssl rand -base64 32
   N8N_USER_MANAGEMENT_JWT_SECRET=your_jwt_secret_here  # Generate: openssl rand -base64 32

   # Flowise Configuration
   FLOWISE_USERNAME=admin
   FLOWISE_PASSWORD=your_flowise_password_here

   # Domain Configuration
   DOMAIN=dev.arumai.ai
   EMAIL=<EMAIL>  # For Let's Encrypt notifications

   # Traefik Dashboard Authentication
   TRAEFIK_AUTH_USER=admin:$2y$10$your_bcrypt_hash_here  # Generate with htpasswd

   # Server Configuration
   SERVER_IP=123.456.789.012  # Your actual server IP
   ```

   **Note**: acme-dns now uses SQLite, so it doesn't need PostgreSQL credentials.

### **Step 2: Generate Traefik Authentication Hash**

Generate a bcrypt hash for Traefik dashboard access:
```bash
# Install htpasswd if not available
sudo apt-get install apache2-utils

# Generate the hash (replace 'your_password' with your actual password)
htpasswd -nb admin your_password

# Copy the output to TRAEFIK_AUTH_USER in your .env file
```

### **Step 3: Run acme-dns Setup**

Run the automated setup script:
```bash
./scripts/setup-acme-dns.sh
```

This script will:
- Configure acme-dns with your settings
- Start postgres and acme-dns services
- Register your domain with acme-dns
- Generate the Traefik configuration
- Provide DNS records to add in GoDaddy

### **Step 4: Add DNS Records in GoDaddy**

The setup script will output DNS records like this. Add them to your GoDaddy domain:

```
Record 1 (acme-dns server):
Type: A
Name: auth.dev
Value: YOUR_SERVER_IP
TTL: 600

Record 2 (wildcard challenge):
Type: CNAME
Name: _acme-challenge.dev
Value: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.auth.dev.arumai.ai
TTL: 600

Record 3 (base domain challenge):
Type: CNAME
Name: _acme-challenge
Value: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.auth.dev.arumai.ai
TTL: 600
```

### **Step 5: Wait for DNS Propagation**

Wait 5-10 minutes for DNS propagation, then test:
```bash
# Test the acme-dns server
nslookup auth.dev.arumai.ai

# Test the CNAME records
nslookup _acme-challenge.dev.arumai.ai
```

### **Step 6: Start Traefik and Services**

1. Start Traefik (this will automatically generate SSL certificates):
   ```bash
   docker-compose up -d traefik
   ```

2. Monitor the certificate generation:
   ```bash
   docker-compose logs -f traefik
   ```

3. Start your services:
   ```bash
   # For CPU-only setup
   docker-compose --profile cpu up -d

   # For GPU setup
   docker-compose --profile gpu-nvidia up -d
   ```

## 🔧 **Configuration Details**

### **Traefik v3 Features Used:**

- **Automatic Service Discovery**: Services are auto-discovered via Docker labels
- **Wildcard SSL**: Single certificate for `*.dev.arumai.ai`
- **HTTP to HTTPS Redirect**: Automatic redirection
- **Dashboard**: Secure dashboard with basic auth
- **Load Balancing**: Built-in load balancing for services

### **Security Features:**

- **HTTPS Only**: All traffic forced to HTTPS
- **Secure Headers**: Automatic security headers
- **Basic Auth**: Dashboard protected with basic authentication
- **Internal Network**: Services communicate on internal Docker network

## 🔍 **Troubleshooting**

### **Certificate Issues:**

1. **Check acme-dns registration**:
   ```bash
   curl -s http://YOUR_SERVER_IP:80/register
   ```

2. **Verify DNS records**:
   ```bash
   dig _acme-challenge.dev.arumai.ai
   ```

3. **Check Traefik logs**:
   ```bash
   docker-compose logs traefik | grep -i error
   ```

### **Service Issues:**

1. **Check service status**:
   ```bash
   docker-compose ps
   ```

2. **Check specific service logs**:
   ```bash
   docker-compose logs flowise
   ```

3. **Test internal connectivity**:
   ```bash
   docker-compose exec traefik wget -O- http://flowise:3001
   ```

### **DNS Issues:**

1. **Test acme-dns server**:
   ```bash
   # Should return your server IP
   nslookup auth.dev.arumai.ai
   ```

2. **Test CNAME resolution**:
   ```bash
   # Should resolve to the acme-dns subdomain
   nslookup _acme-challenge.dev.arumai.ai
   ```

## 📊 **Monitoring and Maintenance**

### **Access Points:**

- **Traefik Dashboard**: https://traefik.dev.arumai.ai
- **Service Health**: Check via dashboard or direct access
- **Logs**: `docker-compose logs <service_name>`

### **Certificate Renewal:**

Certificates are automatically renewed by Traefik. No manual intervention required!

### **Adding New Services:**

To add a new service, just add these labels to your docker-compose service:

```yaml
labels:
  - "traefik.enable=true"
  - "traefik.http.routers.myservice.rule=Host(`myservice.${DOMAIN}`)"
  - "traefik.http.routers.myservice.entrypoints=websecure"
  - "traefik.http.routers.myservice.tls=true"
  - "traefik.http.routers.myservice.tls.certresolver=acmedns"
  - "traefik.http.routers.myservice.tls.domains[0].main=*.${DOMAIN}"
  - "traefik.http.routers.myservice.tls.domains[0].sans=${DOMAIN}"
  - "traefik.http.services.myservice.loadbalancer.server.port=PORT_NUMBER"
```

## 🎉 **Success Verification**

If everything is working correctly:

1. ✅ All services accessible via HTTPS
2. ✅ Valid SSL certificates (no browser warnings)  
3. ✅ HTTP automatically redirects to HTTPS
4. ✅ Traefik dashboard accessible and shows services
5. ✅ Certificate auto-renewal working

## 🔄 **Advantages Over nginx + Certbot**

- **No API Keys**: No GoDaddy API limitations
- **Auto-Discovery**: Services auto-registered via Docker labels
- **Simpler Config**: No manual nginx virtual hosts
- **Better Monitoring**: Built-in dashboard and metrics
- **Cloud Native**: Better suited for containerized environments

Your wildcard SSL setup with Traefik v3 + acme-dns is complete! 🚀 