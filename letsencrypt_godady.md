# GoDaddy + LetsEncrypt Certificate Installation

*Valid as of September 2020*

*note: if you have shell access and want to automatically renew, [follow the steps on this page instead](https://forum.webseodesigners.com/web-design-seo-and-hosting-f16/howto-automatic-free-letsencrypt-ssl-certificates--t3787.html)*

Much of the current documentation on this from [LetsEncryt](https://letsencrypt.org/docs/godaddy/)
and [<PERSON><PERSON><PERSON>](https://ca.godaddy.com/help/install-a-lets-encrypt-certificate-on-your-linux-hosting-account-28023)
suggests that this is a very hard thing to do - but I'm okay with spending 10 minutes every 2-3 months for a 
free, quality SSL certificate. If you are too, here's how I do it.

### Run the certbot command

Here's my certbot command (replace `mydomain.com` and `*.mydomain.com` with your own):

    certbot certonly --manual -d mydomain.com -d *.mydomain.com --preferred-challenges dns-01 --server https://acme-v02.api.letsencrypt.org/directory

### Add TXT Records

From the GoDaddy Admin site, navigate to your site's DNS management screen:

-  Domain Manager (or `Domains`)
-  mysite.com > \[...\] > Manage DNS
-  Add

`Host` will be `_acme-challenge` (ie: the text record name WITHOUT the domain name part) and `TXT Value` will be 
whatever your certbot command prompted. This will have to be done twice if you're using a wildcard like me, since
that counts as two domains. `TTL` can be very short since it's only a one-time thing.

### Add Certificate

Within Godaddy site, navigatate to CPanel SSL/TLS screen:

-  My Hosting (alias `Hosting & Wordpress`, ...and others)
-  \[Site Name\] > \[...\] > Settings
-  File Manager
-  CPanel Home
-  SSL/TLS
-  Click `Manage SSL sites.`

`Certificate: (CRT)` will be the first section of `fullchain.pem` that certbot generated, including the `BEGIN` and
`END` lines.

`Private Key (KEY)` will be the entire file named `privkey.pem` that certbot generated.

`Certificate Authority Bundle: (CABUNDLE)` will be the second section of `fullchain.pem` that certbot generated,
including the `BEGIN` and `END` lines.

Click `Install Certificate` and you're done!
