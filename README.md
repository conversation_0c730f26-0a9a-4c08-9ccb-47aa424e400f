# AI Services Stack with Traefik SSL

A complete AI services stack with <PERSON><PERSON><PERSON><PERSON> reverse proxy and manual SSL certificates.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- SSL certificates already obtained (e.g., from Let's Encrypt)
- DNS records pointing to your server

### 1. Setup
```bash
# Clone and configure
git clone <your-repo>
cd ai-agentic-workflow

# Run setup script
./scripts/setup-manual-certs.sh

# Edit .env file with your configuration
cp env.example .env
# Edit .env with your settings
```

### 2. Start Services
```bash
docker-compose up -d
```

### 3. Access Services
- **Traefik Dashboard**: https://traefik.dev.arumai.ai
- **n8n**: https://n8n.dev.arumai.ai
- **Flowise**: https://flowise.dev.arumai.ai
- **Open WebUI**: https://chat.dev.arumai.ai
- **Qdrant**: https://vector.dev.arumai.ai
- **Ollama**: https://ai.dev.arumai.ai

## 📋 Services Included

- **Traefik** - Reverse proxy with SSL termination
- **n8n** - Workflow automation platform
- **Flowise** - Low-code LLM apps builder
- **Open WebUI** - ChatGPT-like interface for LLMs
- **Qdrant** - Vector database for embeddings
- **Ollama** - Local LLM server
- **PostgreSQL** - Database for n8n

## 🔧 Configuration

### SSL Certificates
Uses manually obtained certificates from `/etc/letsencrypt/live/dev.arumai.ai/`

### Environment Variables
Key variables in `.env`:
- `DOMAIN=dev.arumai.ai`
- `EMAIL=<EMAIL>`
- `SERVER_IP=your.server.ip`
- `TRAEFIK_AUTH_USER=admin:$apr1$...`

## 📚 Documentation

- [Manual Certificate Setup](docs/MANUAL_CERTS.md) - Complete setup guide
- [Port Configuration](docs/PORTS.md) - Port mappings and conflicts
- [Troubleshooting](docs/TROUBLESHOOTING.md) - Common issues and solutions
- [Architecture](docs/ARCHITECTURE.md) - System architecture overview

## 🛠️ Scripts

- `scripts/setup-manual-certs.sh` - Initial setup with certificate validation
- `scripts/check-ports.sh` - Check for port conflicts
- `scripts/test-connectivity.sh` - Test service connectivity

## 🔒 Security Features

- HTTPS enforced for all services
- Security headers via Traefik middleware
- Network isolation between services
- Read-only certificate mounting
- Basic auth for Traefik dashboard

## 📱 GPU Support

For GPU acceleration (Ollama):
```bash
# Start with GPU profile
docker-compose --profile gpu-nvidia up -d
```

## 🔄 Certificate Renewal

Manual renewal required:
```bash
sudo certbot renew
docker-compose restart traefik
```

## 🎯 Key Benefits

✅ Simple setup with existing certificates  
✅ No complex DNS challenges  
✅ Production-ready configuration  
✅ Comprehensive service stack  
✅ Automatic HTTPS for all services  
✅ Local development support
