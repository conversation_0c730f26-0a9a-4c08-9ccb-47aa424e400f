#!/usr/bin/env python3
"""
Test script to verify the Docker setup is working correctly
"""

import requests
import time
import json

def test_health_endpoint():
    """Test the health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ Health endpoint working")
            health_data = response.json()
            print(f"   Services status: {health_data.get('services', {})}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_chat_endpoint():
    """Test the chat endpoint with Ollama"""
    try:
        payload = {
            "messages": [{"role": "user", "content": "Say hello in one word"}],
            "model": "llama3.1",
            "max_tokens": 10
        }
        
        print("🤖 Testing chat with Ollama...")
        response = requests.post(
            "http://localhost:8000/chat", 
            json=payload, 
            timeout=30
        )
        
        if response.status_code == 200:
            chat_data = response.json()
            print(f"✅ Chat working: {chat_data.get('response', 'No response')}")
            return True
        else:
            print(f"❌ Chat failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat error: {e}")
        return False

def test_models_endpoint():
    """Test the models endpoint"""
    try:
        response = requests.get("http://localhost:8000/models", timeout=10)
        if response.status_code == 200:
            models_data = response.json()
            models = models_data.get("models", [])
            print(f"✅ Models endpoint working: {len(models)} models available")
            for model in models[:3]:  # Show first 3 models
                print(f"   - {model.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Models endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Models endpoint error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing AI Agentic Workflows Docker Setup")
    print("=" * 50)
    
    # Wait a bit for services to be ready
    print("⏳ Waiting for services to be ready...")
    time.sleep(5)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Models List", test_models_endpoint),
        ("Chat with Ollama", test_chat_endpoint),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Your setup is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")
        print("\nTroubleshooting tips:")
        print("1. Make sure all services are running: docker-compose ps")
        print("2. Check logs: docker-compose logs ai-agentic-app")
        print("3. Verify Ollama is ready: docker-compose logs ollama")

if __name__ == "__main__":
    main()
