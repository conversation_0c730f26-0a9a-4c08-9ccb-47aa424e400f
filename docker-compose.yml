volumes:
  n8n_storage:
  postgres_storage:
  ollama_storage:
  qdrant_storage:
  open-webui:
  flowise:

networks:
  ainetwork:

x-n8n: &service-n8n
  image: n8nio/n8n:latest
  networks: ['ainetwork']
  environment:
    - DB_TYPE=postgresdb
    - DB_POSTGRESDB_HOST=postgres
    - DB_POSTGRESDB_USER=${POSTGRES_USER}
    - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
    - N8N_DIAGNOSTICS_ENABLED=false
    - N8N_PERSONALIZATION_ENABLED=false
    - N8N_ENCRYPTION_KEY
    - N8N_USER_MANAGEMENT_JWT_SECRET
    - N8N_SECURE_COOKIE=false
    # n8n Traefik-specific settings
    - N8N_HOST=n8n.${DOMAIN}
    - N8N_PORT=5678
    - N8N_PROTOCOL=https
    - WEBHOOK_URL=https://n8n.${DOMAIN}/
    - NODE_ENV=production
  links:
    - postgres

x-ollama: &service-ollama
  image: ollama/ollama:latest
  container_name: ollama
  networks: ['ainetwork']
  restart: unless-stopped
  ports:
    - "11434:11434"
  volumes:
    - ollama_storage:/root/.ollama

x-init-ollama: &init-ollama
  image: ollama/ollama:latest
  networks: ['ainetwork']
  container_name: ollama-pull-llama
  volumes:
    - ollama_storage:/root/.ollama
  entrypoint: /bin/sh
  command:
    - "-c"
    - "sleep 3; OLLAMA_HOST=ollama:11434 ollama pull llama3.1; OLLAMA_HOST=ollama:11434 ollama pull nomic-embed-text"

services:
  traefik:
    image: traefik
    container_name: traefik
    networks: ['ainetwork']
    restart: unless-stopped
    command:
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=ainetwork"

      # File provider for certificates
      - "--providers.file.filename=/config/tls.yml"
      - "--providers.file.watch=true"

      # Entrypoints
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"

      # Global HTTP to HTTPS redirect
      - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
      - "--entrypoints.web.http.redirections.entrypoint.permanent=true"

      # Logging
      - "--log.level=INFO"
      - "--accesslog=true"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./certs/tls.yml:/config/tls.yml:ro
      - ./certs:/certs:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.${DOMAIN}`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=websecure"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      - "traefik.http.routers.traefik-dashboard.tls=true"
      - "traefik.http.routers.traefik-dashboard.middlewares=traefik-auth"
      - "traefik.http.middlewares.traefik-auth.basicauth.users=${TRAEFIK_AUTH_USER}"

  flowise:
    image: flowiseai/flowise
    networks: ['ainetwork']
    restart: unless-stopped
    container_name: flowise
    environment:
        - PORT=3001
        - FLOWISE_USERNAME=${FLOWISE_USERNAME}
        - FLOWISE_PASSWORD=${FLOWISE_PASSWORD}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
        - flowise:/root/.flowise
    entrypoint: /bin/sh -c "sleep 3; flowise start"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.flowise.rule=Host(`flowise.${DOMAIN}`)"
      - "traefik.http.routers.flowise.entrypoints=websecure"
      - "traefik.http.routers.flowise.tls=true"
      - "traefik.http.services.flowise.loadbalancer.server.port=3001"

  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    networks: ['ainetwork']
    restart: unless-stopped
    container_name: open-webui
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - open-webui:/app/backend/data
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.open-webui.rule=Host(`chat.${DOMAIN}`)"
      - "traefik.http.routers.open-webui.entrypoints=websecure"
      - "traefik.http.routers.open-webui.tls=true"
      - "traefik.http.services.open-webui.loadbalancer.server.port=8080"

  postgres:
    image: postgres:16-alpine
    networks: ['ainetwork']
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER
      - POSTGRES_PASSWORD
      - POSTGRES_DB
    volumes:
      - postgres_storage:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -U ${POSTGRES_USER} -d ${POSTGRES_DB}']
      interval: 5s
      timeout: 5s
      retries: 10

  n8n-import:
    <<: *service-n8n
    container_name: n8n-import
    entrypoint: /bin/sh
    command:
      - "-c"
      - "n8n import:credentials --separate --input=/backup/credentials && n8n import:workflow --separate --input=/backup/workflows"
    volumes:
      - ./n8n/backup:/backup
    depends_on:
      postgres:
        condition: service_healthy

  n8n:
    <<: *service-n8n
    container_name: n8n
    restart: unless-stopped
    volumes:
      - n8n_storage:/home/<USER>/.n8n
      - ./n8n/backup:/backup
      - ./shared:/data/shared
    depends_on:
      postgres:
        condition: service_healthy
      n8n-import:
        condition: service_completed_successfully
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.n8n.rule=Host(`n8n.${DOMAIN}`)"
      - "traefik.http.routers.n8n.entrypoints=websecure"
      - "traefik.http.routers.n8n.tls=true"
      - "traefik.http.services.n8n.loadbalancer.server.port=5678"
      # Security headers middleware
      - "traefik.http.routers.n8n.middlewares=n8n-security@docker"
      - "traefik.http.middlewares.n8n-security.headers.SSLRedirect=true"
      - "traefik.http.middlewares.n8n-security.headers.STSSeconds=315360000"
      - "traefik.http.middlewares.n8n-security.headers.browserXSSFilter=true"
      - "traefik.http.middlewares.n8n-security.headers.contentTypeNosniff=true"
      - "traefik.http.middlewares.n8n-security.headers.forceSTSHeader=true"
      - "traefik.http.middlewares.n8n-security.headers.STSIncludeSubdomains=true"
      - "traefik.http.middlewares.n8n-security.headers.STSPreload=true"

  qdrant:
    image: qdrant/qdrant
    container_name: qdrant
    networks: ['ainetwork']
    restart: unless-stopped
    ports:
      - "6333:6333"
    volumes:
      - qdrant_storage:/qdrant/storage
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.qdrant.rule=Host(`vector.${DOMAIN}`)"
      - "traefik.http.routers.qdrant.entrypoints=websecure"
      - "traefik.http.routers.qdrant.tls=true"
      - "traefik.http.services.qdrant.loadbalancer.server.port=6333"

  ollama-cpu:
    profiles: ["cpu"]
    <<: *service-ollama
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ollama.rule=Host(`ai.${DOMAIN}`)"
      - "traefik.http.routers.ollama.entrypoints=websecure"
      - "traefik.http.routers.ollama.tls=true"
      - "traefik.http.services.ollama.loadbalancer.server.port=11434"

  ollama-gpu:
    profiles: ["gpu-nvidia"]
    <<: *service-ollama
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ollama.rule=Host(`ai.${DOMAIN}`)"
      - "traefik.http.routers.ollama.entrypoints=websecure"
      - "traefik.http.routers.ollama.tls=true"
      - "traefik.http.services.ollama.loadbalancer.server.port=11434"

  ollama-pull-llama-cpu:
    profiles: ["cpu"]
    <<: *init-ollama
    depends_on:
      - ollama-cpu

  ollama-pull-llama-gpu:
    profiles: ["gpu-nvidia"]
    <<: *init-ollama
    depends_on:
      - ollama-gpu

  ai-agentic-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-agentic-app
    networks: ['ainetwork']
    restart: unless-stopped
    environment:
      # Ollama endpoint for your application
      - OLLAMA_BASE_URL=http://ollama:11434/v1
      - OLLAMA_API_URL=http://ollama:11434/v1/chat/completions
      # Database connection (if needed)
      - POSTGRES_HOST=postgres
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      # N8N connection (if needed)
      - N8N_URL=http://n8n:5678
      # Other environment variables
      - PYTHONUNBUFFERED=1
    volumes:
      - ./shared:/app/shared
      - ./tiny_agents:/app/tiny_agents
    depends_on:
      postgres:
        condition: service_healthy
      ollama-cpu:
        condition: service_started
        required: false
      ollama-gpu:
        condition: service_started
        required: false
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ai-app.rule=Host(`app.${DOMAIN}`)"
      - "traefik.http.routers.ai-app.entrypoints=websecure"
      - "traefik.http.routers.ai-app.tls=true"
      - "traefik.http.services.ai-app.loadbalancer.server.port=8000"
