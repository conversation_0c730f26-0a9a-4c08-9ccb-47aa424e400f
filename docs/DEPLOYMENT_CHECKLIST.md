# Deployment Checklist

## Pre-Deployment Checklist

### Infrastructure Requirements
- [ ] Ubuntu 20.04+ VPS server provisioned
- [ ] Server has minimum 4GB RAM, 2 CPU cores, 50GB storage
- [ ] Domain name configured and DNS pointing to server IP
- [ ] SSL certificates obtained (manual or Let's Encrypt)
- [ ] SSH access configured with key-based authentication

### Local Environment Setup
- [ ] Git repository cloned locally
- [ ] SSH keys generated and added to GitHub
- [ ] Docker and Docker Compose installed locally for testing
- [ ] Ansible installed locally (for manual deployment)
- [ ] Required secrets and environment variables prepared

### Security Preparation
- [ ] Strong passwords generated for all services
- [ ] SSH keys generated for server access
- [ ] Firewall rules planned (ports 22, 80, 443)
- [ ] Backup strategy defined

## Manual Deployment Checklist

### Server Setup (Based on History)
- [ ] **Update system packages**
  ```bash
  sudo apt-get update && sudo apt-get upgrade -y
  ```

- [ ] **Install Docker**
  ```bash
  # Add Docker GPG key
  sudo install -m 0755 -d /etc/apt/keyrings
  sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
  sudo chmod a+r /etc/apt/keyrings/docker.asc
  
  # Add repository
  echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
  $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
  
  # Install Docker packages
  sudo apt-get update
  sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
  
  # Add user to docker group
  sudo usermod -aG docker executor
  ```

- [ ] **Configure SSH for GitHub**
  ```bash
  mkdir -p ~/.ssh/
  ssh-keygen -t ed25519 -C "executor@balaji-github" -f ~/.ssh/github_access_id
  
  # Add to ~/.ssh/config
  echo "Host github.com
      HostName github.com
      User git
      IdentityFile ~/.ssh/github_access_id
      IdentitiesOnly yes" >> ~/.ssh/config
  
  chmod 600 ~/.ssh/config
  ```

- [ ] **Clone repository**
  ```bash
  <NAME_EMAIL>:jbx09/ai-agentic-workflows.git
  cd ai-agentic-workflows/
  ```

- [ ] **Configure environment**
  ```bash
  cp env.example .env
  # Edit .env with production values
  ```

- [ ] **Setup SSL certificates**
  ```bash
  mkdir -p certs/
  # Copy SSL certificates and configure tls.yml
  ```

- [ ] **Configure firewall**
  ```bash
  sudo ufw allow ssh
  sudo ufw allow 80/tcp
  sudo ufw allow 443/tcp
  sudo ufw enable
  ```

- [ ] **Deploy services**
  ```bash
  docker compose --profile cpu up -d
  ```

- [ ] **Verify deployment**
  ```bash
  docker ps
  docker logs n8n
  docker logs flowise
  ```

### Service Verification
- [ ] Traefik dashboard accessible
- [ ] n8n interface loading
- [ ] Flowise interface loading
- [ ] Open WebUI accessible
- [ ] Qdrant dashboard accessible
- [ ] Ollama API responding
- [ ] SSL certificates valid

## Automation Implementation Checklist

### Ansible Setup
- [ ] **Create Ansible directory structure**
  ```bash
  mkdir -p ansible/{inventory,playbooks,roles,group_vars,host_vars,templates}
  ```

- [ ] **Configure Ansible**
  - [ ] Create `ansible.cfg`
  - [ ] Setup inventory files for each environment
  - [ ] Create group variables files
  - [ ] Setup Ansible Vault for secrets

- [ ] **Develop Ansible roles**
  - [ ] Common role (base packages, users, firewall)
  - [ ] Docker role (installation and configuration)
  - [ ] SSL role (certificate management)
  - [ ] Services role (application deployment)
  - [ ] Monitoring role (observability stack)

- [ ] **Create playbooks**
  - [ ] Site playbook (main deployment)
  - [ ] Server setup playbook
  - [ ] Service deployment playbook
  - [ ] SSL management playbook

- [ ] **Test Ansible deployment**
  ```bash
  # Syntax check
  ansible-playbook --syntax-check playbooks/site.yml
  
  # Dry run
  ansible-playbook -i inventory/development.yml playbooks/site.yml --check
  
  # Deploy to development
  ansible-playbook -i inventory/development.yml playbooks/site.yml
  ```

### GitHub Actions Setup
- [ ] **Create workflow files**
  - [ ] CI workflow (testing and validation)
  - [ ] Development deployment workflow
  - [ ] Staging deployment workflow
  - [ ] Production deployment workflow

- [ ] **Configure GitHub secrets**
  - [ ] SSH_PRIVATE_KEY
  - [ ] ANSIBLE_VAULT_PASSWORD
  - [ ] N8N_API_TOKEN
  - [ ] FLOWISE_API_TOKEN
  - [ ] SLACK_WEBHOOK

- [ ] **Setup environments in GitHub**
  - [ ] Development environment
  - [ ] Staging environment
  - [ ] Production environment (with protection rules)

- [ ] **Test CI/CD pipeline**
  - [ ] Push to develop branch triggers development deployment
  - [ ] Push to main triggers staging deployment
  - [ ] Manual production deployment works
  - [ ] Workflow synchronization scripts work

### Workflow Synchronization
- [ ] **Create export/import scripts**
  - [ ] n8n workflow export script
  - [ ] n8n workflow import script
  - [ ] Flowise chatflow export script
  - [ ] Flowise chatflow import script

- [ ] **Test workflow migration**
  - [ ] Export workflows from development
  - [ ] Import workflows to staging
  - [ ] Validate workflows function correctly

## Post-Deployment Checklist

### Service Configuration
- [ ] **n8n Setup**
  - [ ] Create admin user account
  - [ ] Configure webhook URLs
  - [ ] Import initial workflows
  - [ ] Test workflow execution

- [ ] **Flowise Setup**
  - [ ] Login with configured credentials
  - [ ] Import chatflows
  - [ ] Configure AI model connections
  - [ ] Test chatflow functionality

- [ ] **Ollama Configuration**
  - [ ] Verify model downloads completed
  - [ ] Test model inference
  - [ ] Configure model parameters

### Monitoring & Maintenance
- [ ] **Setup monitoring** (if implemented)
  - [ ] Prometheus metrics collection
  - [ ] Grafana dashboards
  - [ ] Alert rules configuration
  - [ ] Notification channels

- [ ] **Backup procedures**
  - [ ] Database backup script
  - [ ] Volume backup procedure
  - [ ] Configuration backup
  - [ ] Test restore procedure

- [ ] **Security hardening**
  - [ ] Change default passwords
  - [ ] Review firewall rules
  - [ ] Enable fail2ban (if configured)
  - [ ] Setup log monitoring

### Documentation Updates
- [ ] Update service URLs in documentation
- [ ] Document any environment-specific configurations
- [ ] Update troubleshooting guide with encountered issues
- [ ] Create runbook for common operations

## Production Readiness Checklist

### Performance & Scalability
- [ ] Resource utilization monitoring implemented
- [ ] Database performance optimized
- [ ] Caching strategies implemented
- [ ] Load balancing configured (if multi-server)

### Security
- [ ] Security scanning implemented in CI/CD
- [ ] Secrets rotation procedure established
- [ ] Access control properly configured
- [ ] Network security implemented

### Reliability
- [ ] Health checks implemented for all services
- [ ] Auto-restart policies configured
- [ ] Backup and restore procedures tested
- [ ] Disaster recovery plan documented

### Compliance
- [ ] Logging and audit trails configured
- [ ] Data retention policies implemented
- [ ] Privacy considerations addressed
- [ ] Compliance requirements met

## Environment-Specific Checklists

### Development Environment
- [ ] Fast iteration capabilities
- [ ] Debug logging enabled
- [ ] Development tools accessible
- [ ] Test data loaded

### Staging Environment
- [ ] Production-like configuration
- [ ] Integration testing enabled
- [ ] Performance testing possible
- [ ] User acceptance testing ready

### Production Environment
- [ ] High availability configured
- [ ] Monitoring and alerting active
- [ ] Backup procedures automated
- [ ] Security hardening complete
- [ ] Change management process in place

## Automation Verification

### Ansible Verification
- [ ] Idempotent playbook execution
- [ ] All environments deployable via Ansible
- [ ] Secrets properly encrypted with Vault
- [ ] Role-based deployment working

### CI/CD Verification
- [ ] Automated testing passes
- [ ] Development deployment automated
- [ ] Staging deployment automated
- [ ] Production deployment requires approval
- [ ] Rollback procedure working

### Workflow Synchronization
- [ ] Development to staging sync working
- [ ] Staging to production sync working
- [ ] Configuration differences handled
- [ ] Data migration procedures working

## Final Verification

- [ ] All services accessible via HTTPS
- [ ] SSL certificates valid and auto-renewing
- [ ] All functionality tested end-to-end
- [ ] Performance meets requirements
- [ ] Security scan passes
- [ ] Backup and restore tested
- [ ] Documentation complete and accurate
- [ ] Team training completed
- [ ] Monitoring and alerting verified
- [ ] Support procedures in place

## Rollback Plan

- [ ] Previous version deployment procedure documented
- [ ] Data rollback procedure documented
- [ ] Configuration rollback procedure documented
- [ ] Service restoration procedure documented
- [ ] Rollback triggers and criteria defined

## Success Criteria

- [ ] All services responding correctly
- [ ] Workflows executing successfully
- [ ] Performance within acceptable limits
- [ ] Security requirements met
- [ ] Automation working as expected
- [ ] Team able to operate and maintain system
- [ ] Documentation complete and accessible
- [ ] Monitoring providing adequate visibility

---

## Quick Commands Reference

### Service Management
```bash
# Check status
docker compose ps

# View logs
docker compose logs -f [service_name]

# Restart service
docker compose restart [service_name]

# Update services
docker compose pull && docker compose up -d
```

### Ansible Commands
```bash
# Deploy all
ansible-playbook -i inventory/production.yml playbooks/site.yml

# Deploy specific service
ansible-playbook -i inventory/production.yml playbooks/services-deploy.yml

# Check configuration
ansible-playbook -i inventory/production.yml playbooks/site.yml --check
```

### Health Check URLs
- Traefik: `https://traefik.${DOMAIN}/dashboard/`
- n8n: `https://n8n.${DOMAIN}`
- Flowise: `https://flowise.${DOMAIN}`
- Open WebUI: `https://chat.${DOMAIN}`
- Qdrant: `https://vector.${DOMAIN}`
- Ollama: `https://ai.${DOMAIN}` 