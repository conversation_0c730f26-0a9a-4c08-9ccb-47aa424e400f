# Ubuntu Server Setup Guide

## Overview

This guide provides step-by-step instructions for setting up an Ubuntu VPS server to host the AI Agentic Workflow services. The setup includes Dock<PERSON>, Docker Compose, and all necessary dependencies for running n8n, Flowise, Ollama, and other AI services.

## Prerequisites

- Ubuntu 20.04+ VPS server
- Root or sudo access
- Domain name configured to point to your server IP
- Basic knowledge of Linux command line

## 1. Initial Server Setup

### 1.1 Update System Packages

```bash
sudo apt-get update
sudo apt-get upgrade -y
```

### 1.2 Create Non-Root User (if not exists)

```bash
# Create user
sudo adduser executor

# Add to sudo group
sudo usermod -aG sudo executor

# Switch to new user
su - executor
```

## 2. Docker Installation

### 2.1 Install Required Dependencies

```bash
sudo apt-get install ca-certificates curl gnupg lsb-release
```

### 2.2 Add Docker's Official GPG Key

```bash
sudo install -m 0755 -d /etc/apt/keyrings
sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
sudo chmod a+r /etc/apt/keyrings/docker.asc
```

### 2.3 Add Docker Repository

```bash
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
$(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
```

### 2.4 Install Docker Packages

```bash
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

### 2.5 Configure Docker for Non-Root User

```bash
# Add current user to docker group
sudo usermod -aG docker $USER

# Logout and login again, or use:
newgrp docker

# Test Docker installation
docker run hello-world
```

### 2.6 Enable Docker Service

```bash
sudo systemctl enable docker
sudo systemctl start docker
sudo systemctl status docker
```

## 3. SSH Key Setup for GitHub Access

### 3.1 Generate SSH Key

```bash
# Create .ssh directory if it doesn't exist
mkdir -p ~/.ssh/

# Generate SSH key for GitHub
ssh-keygen -t ed25519 -C "executor@balaji-github" -f ~/.ssh/github_access_id

# Display public key to copy to GitHub
cat ~/.ssh/github_access_id.pub
```

### 3.2 Configure SSH for GitHub

Create SSH configuration file:

```bash
vi ~/.ssh/config
```

Add the following content:

```
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/github_access_id
    IdentitiesOnly yes
```

Set proper permissions:

```bash
chmod 600 ~/.ssh/config
chmod 600 ~/.ssh/github_access_id
chmod 644 ~/.ssh/github_access_id.pub
```

### 3.3 Test GitHub Connection

```bash
ssh -T **************
```

## 4. Clone and Setup Project

### 4.1 Clone Repository

```bash
<NAME_EMAIL>:jbx09/ai-agentic-workflows.git
cd ai-agentic-workflows/
```

### 4.2 Configure Environment Variables

```bash
# Copy example environment file
cp env.example .env

# Edit environment variables
vi .env
```

### 4.3 Required Environment Variables

Update the following variables in `.env`:

```bash
# Database Configuration
POSTGRES_USER=arumai
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=arumai_db

# N8N Configuration
N8N_ENCRYPTION_KEY=your_32_character_encryption_key
N8N_USER_MANAGEMENT_JWT_SECRET=your_jwt_secret_here

# Flowise Configuration
FLOWISE_USERNAME=admin
FLOWISE_PASSWORD=your_flowise_password_here

# Domain Configuration
DOMAIN=your-domain.com
EMAIL=<EMAIL>

# Traefik Dashboard Authentication
TRAEFIK_AUTH_USER=admin:$2y$10$hashed_password_here

# Server Configuration
SERVER_IP=your_server_ip_here
```

### 4.4 Generate Required Secrets

```bash
# Generate N8N encryption key (32 characters)
openssl rand -hex 16

# Generate JWT secret
openssl rand -hex 32

# Generate Traefik auth hash
htpasswd -nb admin your_password
```

## 5. SSL Certificate Setup

### 5.1 Manual Certificate Preparation

If using manual SSL certificates, ensure they are placed in the `certs/` directory:

```bash
# Create certificates directory
mkdir -p certs/

# Copy your certificates
# - certs/your-domain.com.crt
# - certs/your-domain.com.key
# - certs/tls.yml (Traefik configuration)
```

Refer to `docs/MANUAL_CERTS.md` for detailed certificate setup instructions.

## 6. Firewall Configuration

### 6.1 Configure UFW (Uncomplicated Firewall)

```bash
# Check firewall status
sudo ufw status

# Allow SSH (if not already allowed)
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow specific service ports (if needed for direct access)
sudo ufw allow 5678/tcp  # n8n
sudo ufw allow 3001/tcp  # Flowise
sudo ufw allow 11434/tcp # Ollama
sudo ufw allow 6333/tcp  # Qdrant

# Enable firewall
sudo ufw enable
```

## 7. Start Services

### 7.1 Deploy Stack with CPU Profile

```bash
# Start all services using CPU profile
docker compose --profile cpu up -d

# Check running containers
docker ps

# View logs for specific services
docker logs -f n8n
docker logs -f flowise
docker logs -f ollama
```

### 7.2 Verify Service Health

```bash
# Check if all containers are running
docker compose ps

# Test service endpoints
curl -k https://n8n.your-domain.com
curl -k https://flowise.your-domain.com
curl -k https://chat.your-domain.com
```

## 8. Service URLs

After successful deployment, your services will be available at:

- **n8n**: https://n8n.your-domain.com
- **Flowise**: https://flowise.your-domain.com
- **Open WebUI**: https://chat.your-domain.com
- **Qdrant**: https://vector.your-domain.com
- **Ollama**: https://ai.your-domain.com
- **Traefik Dashboard**: https://traefik.your-domain.com

## 9. Initial Service Configuration

### 9.1 n8n Setup

1. Access n8n at https://n8n.your-domain.com
2. Create admin user account
3. Import workflows from `n8n-tool-workflows/` directory

### 9.2 Flowise Setup

1. Access Flowise at https://flowise.your-domain.com
2. Login with credentials from `.env` file
3. Import chatflows from JSON files

### 9.3 Ollama Model Installation

The setup automatically pulls `llama3.1` and `nomic-embed-text` models. To add more:

```bash
# Access Ollama container
docker exec -it ollama bash

# Pull additional models
ollama pull codellama
ollama pull mistral
```

## 10. Maintenance Commands

### 10.1 Update Services

```bash
# Pull latest images
docker compose pull

# Restart services with new images
docker compose --profile cpu up -d --force-recreate
```

### 10.2 Backup Data

```bash
# Backup Docker volumes
docker run --rm -v ai-agentic-workflows_n8n_storage:/data -v $(pwd):/backup alpine tar czf /backup/n8n_backup.tar.gz -C /data .
docker run --rm -v ai-agentic-workflows_postgres_storage:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .
```

### 10.3 Monitor Logs

```bash
# Follow logs for all services
docker compose logs -f

# Follow logs for specific service
docker compose logs -f n8n
```

## 11. Troubleshooting

### 11.1 Common Issues

1. **Service not starting**: Check logs with `docker logs <container_name>`
2. **SSL certificate issues**: Verify certificate files in `certs/` directory
3. **Database connection issues**: Check PostgreSQL container health
4. **Network issues**: Verify firewall settings and domain DNS

### 11.2 Useful Debug Commands

```bash
# Check container status
docker compose ps

# Inspect container configuration
docker inspect <container_name>

# Check network connectivity
docker network ls
docker network inspect ai-agentic-workflows_ainetwork

# Restart specific service
docker compose restart <service_name>
```

## 12. Next Steps

1. **Automation Setup**: Configure Ansible for automated deployments (see `docs/AUTOMATION.md`)
2. **CI/CD Pipeline**: Set up GitHub Actions for automated testing and deployment
3. **Monitoring**: Implement monitoring solutions (Prometheus, Grafana)
4. **Production Setup**: Follow production deployment guidelines for scalability

## Security Notes

- Change all default passwords in `.env` file
- Regularly update system packages and Docker images
- Monitor logs for suspicious activity
- Implement proper backup strategies
- Use strong SSL certificates
- Consider implementing fail2ban for SSH protection

## Support

For additional help, refer to:
- `docs/TROUBLESHOOTING.md` for common issues
- `docs/ARCHITECTURE.md` for system architecture details
- Service-specific documentation in respective directories 