# AI Agentic Workflow - System Architecture

## Overview

This document describes the architecture of the AI Agentic Workflow platform, including current implementation, future automation plans, and deployment strategies for development, staging, and production environments.

## Architecture Diagram

```
Dev Environment                           Staging/Prod Environment
┌─────────────────────────────────┐       ┌──────────────────────────────────┐
│ Kubernetes?                     │       │ DB (Future consideration)       │
│ ┌─────────────────────────────┐ │       │                                  │
│ │ Docker                      │ │       │ Scalability & reliability?      │
│ │ Docker Compose              │ │       │                                  │
│ └─────────────────────────────┘ │       │ Security?                        │
│                                 │       │ ┌──────────────────────────────┐ │
│ ┌─────────────────────────────┐ │       │ │ Vault - Storing secrets      │ │
│ │ GitHub                      │ │       │ └──────────────────────────────┘ │
│ │ actions/Jenkins(CI/CD)      │ │       │                                  │
│ └─────────────────────────────┘ │       │ ┌──────────────────────────────┐ │
│                                 │       │ │ Network security             │ │
│ ┌─────────────────────────────┐ │       │ └──────────────────────────────┘ │
│ │ LLM - Cloud provider        │ │       │                                  │
│ └─────────────────────────────┘ │       │ ┌──────────────────────────────┐ │
│                                 │       │ │ Load balancer                │ │
│ ┌─────────────────────────────┐ │       │ │ Separate box for prod        │ │
│ │ Backend application (Python │ │       │ └──────────────────────────────┘
│ │ FastAPI)                    │ │       └──────────────────────────────────┘
│ │ ┌─────────────────────────┐ │ │
│ │ │ LLM - local ollama      │ │ │
│ │ │ VectorDB - Qdrant       │ │ │
│ │ │ n8n                     │ │ │
│ │ │ Flowise                 │ │ │
│ │ └─────────────────────────┘ │ │
│ └─────────────────────────────┘ │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ Frontend                    │ │
│ │ Next.js/Vue.js              │ │
│ └─────────────────────────────┘ │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ DB - MongoDB (nosql)        │ │
│ └─────────────────────────────┘ │
│                                 │
│ ┌─────────────────────────────┐ │
│ │ Ansible/Terraform           │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

## Current Implementation

### Core Services

#### 1. Reverse Proxy & Load Balancer
- **Traefik**: Modern reverse proxy with automatic HTTPS
- **Features**: 
  - SSL termination
  - Service discovery
  - Load balancing
  - Dashboard monitoring
- **Domains**: 
  - Dashboard: `traefik.${DOMAIN}`

#### 2. AI & Workflow Orchestration
- **n8n**: Workflow automation platform
  - URL: `n8n.${DOMAIN}`
  - Database: PostgreSQL
  - Features: Workflow automation, API integrations
  
- **Flowise**: AI chatflow builder
  - URL: `flowise.${DOMAIN}`
  - Features: Visual chatflow creation, LLM integration

#### 3. AI & Machine Learning
- **Ollama**: Local LLM hosting
  - URL: `ai.${DOMAIN}`
  - Models: llama3.1, nomic-embed-text
  - Profiles: CPU (default), GPU (nvidia)
  
- **Open WebUI**: Chat interface for AI models
  - URL: `chat.${DOMAIN}`
  - Features: Multi-model chat interface

#### 4. Vector Database
- **Qdrant**: Vector similarity search
  - URL: `vector.${DOMAIN}`
  - Features: Embeddings storage, semantic search

#### 5. Data Storage
- **PostgreSQL**: Primary database for n8n
  - Port: 5432
  - Features: ACID compliance, JSON support

### Network Architecture

#### Docker Network
- **Network**: `ainetwork` (bridge)
- **Service Discovery**: Automatic via Docker Compose
- **Internal Communication**: Container names as hostnames

#### External Access
- **HTTP/HTTPS**: Ports 80/443 via Traefik
- **Direct Access Ports** (optional):
  - PostgreSQL: 5432
  - Ollama: 11434
  - Qdrant: 6333

### Security Implementation

#### SSL/TLS
- **Method**: Manual certificates via Let's Encrypt
- **Configuration**: File-based provider in Traefik
- **Renewal**: Manual process (to be automated)

#### Authentication
- **Traefik Dashboard**: Basic Auth
- **Flowise**: Username/Password
- **n8n**: Built-in user management
- **Services**: Internal network isolation

#### Network Security
- **Firewall**: UFW configured for necessary ports
- **Container Isolation**: Docker networks
- **Reverse Proxy**: Single entry point via Traefik

## Development Environment

### Local Development Setup
```bash
# Clone repository
<NAME_EMAIL>:jbx09/ai-agentic-workflows.git
cd ai-agentic-workflows/

# Configure environment
cp env.example .env
# Edit .env with development values

# Start services
docker compose --profile cpu up -d
```

### Development Services
- **Domain**: `dev.arumai.ai` or `localhost`
- **Profile**: CPU-only for development
- **Database**: Local PostgreSQL container
- **Storage**: Docker volumes for persistence

## Staging/Production Environment (Planned)

### Infrastructure Components

#### 1. Scalability & Reliability
- **Load Balancer**: Separate dedicated instance
- **Database**: External managed database service
- **Storage**: Persistent volumes or cloud storage
- **Monitoring**: Prometheus + Grafana stack

#### 2. Security Enhancements
- **Secrets Management**: HashiCorp Vault integration
- **Network Security**: 
  - VPC/Private networks
  - WAF (Web Application Firewall)
  - DDoS protection
- **Certificate Management**: Automated renewal
- **Access Control**: Role-based authentication

#### 3. Production Deployment Strategy
- **Blue-Green Deployment**: Zero-downtime updates
- **Health Checks**: Service monitoring and auto-recovery
- **Backup Strategy**: Automated backups with retention
- **Disaster Recovery**: Multi-region setup (future)

## Automation & CI/CD Pipeline

### Current State
- **Manual Deployment**: Docker Compose on single server
- **Configuration**: Environment variables and manual setup
- **Workflow Management**: Manual import/export of n8n and Flowise configs

### Planned Automation

#### 1. Infrastructure as Code
```yaml
# Ansible Playbook Structure
playbooks/
├── common.yml              # Common server setup
├── docker.yml              # Docker installation
├── ssl.yml                 # SSL certificate management
├── services.yml            # Service deployment
└── monitoring.yml          # Monitoring setup

inventory/
├── development.yml         # Dev environment hosts
├── staging.yml            # Staging environment hosts
└── production.yml         # Production environment hosts
```

#### 2. GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy AI Agentic Workflow

on:
  push:
    branches: [main, develop]
  
jobs:
  test:
    # Run tests, linting, security scans
    
  deploy-dev:
    # Deploy to development environment
    
  deploy-staging:
    # Deploy to staging environment
    
  deploy-prod:
    # Deploy to production (manual approval)
```

#### 3. Workflow Synchronization
- **Development to Staging**: Automated export/import of n8n workflows
- **Staging to Production**: Manual approval process with testing
- **Configuration Management**: Environment-specific variables
- **Database Migrations**: Automated schema updates

### Automation Tools

#### Ansible Playbooks
- **Server Provisioning**: Ubuntu server setup, Docker installation
- **SSL Management**: Certificate deployment and renewal
- **Service Deployment**: Docker Compose orchestration
- **Monitoring Setup**: Logging and metrics collection

#### GitHub Actions
- **Continuous Integration**: Code testing and validation
- **Continuous Deployment**: Automated deployments to environments
- **Workflow Testing**: n8n and Flowise workflow validation
- **Security Scanning**: Container and dependency vulnerability checks

## Data Flow Architecture

### Workflow Processing
```
User Request → Traefik → n8n/Flowise → Ollama/External AI → Vector DB → Response
```

### Data Storage Layers
1. **Application Data**: PostgreSQL (n8n workflows, users)
2. **Vector Embeddings**: Qdrant (semantic search data)
3. **AI Models**: Ollama storage (local model files)
4. **Configuration**: Docker volumes (service configurations)

### Integration Points
- **n8n ↔ Flowise**: Webhook integrations
- **Flowise ↔ Ollama**: AI model inference
- **n8n ↔ Qdrant**: Vector operations
- **External APIs**: Through n8n and Flowise

## Monitoring & Observability (Planned)

### Metrics Collection
- **System Metrics**: CPU, Memory, Disk, Network
- **Application Metrics**: Response times, error rates
- **AI Metrics**: Model inference times, token usage
- **Business Metrics**: Workflow executions, user activity

### Logging Strategy
- **Centralized Logging**: ELK Stack or similar
- **Log Levels**: Configurable per service
- **Log Retention**: Environment-specific policies
- **Alerting**: Critical error notifications

### Health Checks
- **Service Health**: HTTP endpoints for each service
- **Database Health**: Connection and query performance
- **AI Model Health**: Model availability and response time
- **External Dependencies**: API availability checks

## Backup & Disaster Recovery

### Backup Strategy
- **Database Backups**: Daily automated PostgreSQL dumps
- **Volume Backups**: Docker volume snapshots
- **Configuration Backups**: Git-based configuration management
- **AI Model Backups**: Ollama model storage backup

### Recovery Procedures
- **Service Recovery**: Docker Compose recreation
- **Data Recovery**: Volume restoration from backups
- **Configuration Recovery**: Git-based rollback
- **Full Environment Recovery**: Ansible playbook execution

## Scalability Considerations

### Horizontal Scaling
- **Load Balancing**: Multiple application instances
- **Database Scaling**: Read replicas, sharding
- **AI Models**: Model server clustering
- **Storage**: Distributed storage solutions

### Vertical Scaling
- **Resource Allocation**: CPU and memory optimization
- **Container Limits**: Proper resource constraints
- **Database Tuning**: Performance optimization
- **Cache Layers**: Redis for frequently accessed data

## Security Architecture

### Network Security
- **Firewall Rules**: Minimal required ports
- **Network Segmentation**: Service isolation
- **VPN Access**: Administrative access control
- **SSL/TLS**: End-to-end encryption

### Application Security
- **Authentication**: Multi-factor authentication
- **Authorization**: Role-based access control
- **Input Validation**: Request sanitization
- **Secrets Management**: Encrypted storage

### Compliance & Auditing
- **Access Logs**: Comprehensive audit trails
- **Security Scanning**: Regular vulnerability assessments
- **Data Protection**: GDPR/CCPA compliance
- **Incident Response**: Security incident procedures

## Future Enhancements

### Short Term (3-6 months)
- [ ] Ansible automation implementation
- [ ] GitHub Actions CI/CD pipeline
- [ ] Automated SSL certificate renewal
- [ ] Basic monitoring with Prometheus

### Medium Term (6-12 months)
- [ ] Production environment setup
- [ ] HashiCorp Vault integration
- [ ] Advanced monitoring and alerting
- [ ] Blue-green deployment strategy

### Long Term (12+ months)
- [ ] Multi-region deployment
- [ ] Advanced AI model management
- [ ] Kubernetes migration (if needed)
- [ ] Enterprise security features

## Configuration Management

### Environment Variables
```bash
# Development
DOMAIN=dev.arumai.ai
ENV=development

# Staging
DOMAIN=staging.arumai.ai
ENV=staging

# Production
DOMAIN=arumai.ai
ENV=production
```

### Service Configuration
- **Traefik**: File-based configuration
- **n8n**: Environment variables and database
- **Flowise**: Environment variables
- **Ollama**: Model configuration files

### Secrets Management
- **Current**: Environment variables in `.env` files
- **Planned**: HashiCorp Vault integration
- **Best Practices**: Rotation policies, encryption at rest

## Conclusion

The current architecture provides a solid foundation for AI agentic workflows with room for significant enhancement through automation and production-ready features. The planned automation with Ansible and GitHub Actions will enable reliable, scalable deployments across multiple environments while maintaining security and operational excellence. 