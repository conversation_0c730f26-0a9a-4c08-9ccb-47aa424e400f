# Automation Guide - Ansible & GitHub Actions

## Overview

This guide outlines the automation strategy for the AI Agentic Workflow platform, including Infrastructure as Code (IaC) with Ansible and Continuous Integration/Continuous Deployment (CI/CD) with GitHub Actions.

## Automation Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    GitHub Repository                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Source Code   │  │   Ansible       │  │ GitHub Actions  │  │
│  │   - Docker      │  │   Playbooks     │  │   - CI/CD       │  │
│  │   - Configs     │  │   - Inventory   │  │   - CI/CD       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    CI/CD Pipeline                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │      Test       │  │     Build       │  │     Deploy      │  │
│  │   - Lint        │  │   - Docker      │  │   - Ansible     │  │
│  │   - Security    │  │   - Validate    │  │   - Services    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Target Environments                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Development   │  │     Staging     │  │   Production    │  │
│  │   - Auto deploy │  │   - Auto deploy │  │ - Manual approve│  │
│  │   - Feature test│  │   - Integration │  │ - Blue/Green    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## Ansible Infrastructure as Code

### Directory Structure

```
ansible/
├── ansible.cfg                 # Ansible configuration
├── inventory/                  # Environment inventories
│   ├── development.yml
│   ├── staging.yml
│   └── production.yml
├── playbooks/                  # Main playbooks
│   ├── site.yml               # Main deployment playbook
│   ├── server-setup.yml       # Initial server setup
│   ├── docker-setup.yml       # Docker installation
│   ├── ssl-setup.yml         # SSL certificate management
│   ├── services-deploy.yml    # Service deployment
│   └── monitoring-setup.yml   # Monitoring installation
├── roles/                     # Ansible roles
│   ├── common/
│   ├── docker/
│   ├── ssl/
│   ├── services/
│   └── monitoring/
├── group_vars/                # Group variables
│   ├── all.yml
│   ├── development.yml
│   ├── staging.yml
│   └── production.yml
├── host_vars/                 # Host-specific variables
└── templates/                 # Configuration templates
    ├── docker-compose.yml.j2
    ├── .env.j2
    └── nginx.conf.j2
```

### Ansible Configuration

#### ansible.cfg
```ini
[defaults]
inventory = inventory
host_key_checking = False
retry_files_enabled = False
roles_path = roles
vault_password_file = .vault_pass
gathering = smart
fact_caching = memory
stdout_callback = yaml
bin_ansible_callbacks = True

[ssh_connection]
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o UserKnownHostsFile=/dev/null -o IdentitiesOnly=yes
pipelining = True
```

### Inventory Files

#### inventory/development.yml
```yaml
all:
  children:
    development:
      hosts:
        dev-server:
          ansible_host: dev.arumai.ai
          ansible_user: executor
          ansible_ssh_private_key_file: ~/.ssh/id_ed25519
          environment: development
          domain: dev.arumai.ai
```

#### inventory/production.yml
```yaml
all:
  children:
    production:
      hosts:
        prod-server:
          ansible_host: prod.arumai.ai
          ansible_user: executor
          ansible_ssh_private_key_file: ~/.ssh/id_ed25519
          environment: production
          domain: arumai.ai
        load-balancer:
          ansible_host: lb.arumai.ai
          ansible_user: executor
          role: load_balancer
```

### Main Playbooks

#### playbooks/site.yml
```yaml
---
- name: Deploy AI Agentic Workflow Platform
  hosts: all
  become: yes
  gather_facts: yes
  
  roles:
    - common
    - docker
    - ssl
    - services
    - monitoring
  
  post_tasks:
    - name: Verify service health
      uri:
        url: "https://{{ domain }}/health"
        method: GET
        status_code: 200
      delegate_to: localhost
      run_once: true
```

#### playbooks/server-setup.yml
```yaml
---
- name: Initial Server Setup
  hosts: all
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Update package cache
      apt:
        update_cache: yes
        cache_valid_time: 3600
    
    - name: Install essential packages
      apt:
        name:
          - curl
          - wget
          - git
          - htop
          - vim
          - ufw
          - fail2ban
        state: present
    
    - name: Configure firewall
      ufw:
        rule: allow
        port: "{{ item }}"
        proto: tcp
      loop:
        - "22"    # SSH
        - "80"    # HTTP
        - "443"   # HTTPS
    
    - name: Enable firewall
      ufw:
        state: enabled
        policy: deny
        direction: incoming
```

### Ansible Roles

#### roles/docker/tasks/main.yml
```yaml
---
- name: Install Docker dependencies
  apt:
    name:
      - ca-certificates
      - curl
      - gnupg
      - lsb-release
    state: present

- name: Add Docker GPG key
  apt_key:
    url: https://download.docker.com/linux/ubuntu/gpg
    state: present

- name: Add Docker repository
  apt_repository:
    repo: "deb [arch=amd64] https://download.docker.com/linux/ubuntu {{ ansible_distribution_release }} stable"
    state: present

- name: Install Docker packages
  apt:
    name:
      - docker-ce
      - docker-ce-cli
      - containerd.io
      - docker-buildx-plugin
      - docker-compose-plugin
    state: present
    update_cache: yes

- name: Add user to docker group
  user:
    name: "{{ ansible_user }}"
    groups: docker
    append: yes

- name: Start and enable Docker service
  systemd:
    name: docker
    state: started
    enabled: yes
```

#### roles/services/tasks/main.yml
```yaml
---
- name: Create application directory
  file:
    path: /opt/ai-agentic-workflows
    state: directory
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: '0755'

- name: Clone repository
  git:
    repo: "{{ git_repo_url }}"
    dest: /opt/ai-agentic-workflows
    version: "{{ git_branch | default('main') }}"
    force: yes
  become_user: "{{ ansible_user }}"

- name: Generate environment file
  template:
    src: .env.j2
    dest: /opt/ai-agentic-workflows/.env
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: '0600'

- name: Deploy services with Docker Compose
  community.docker.docker_compose:
    project_src: /opt/ai-agentic-workflows
    profiles: "{{ docker_profiles }}"
    state: present
    pull: yes
  become_user: "{{ ansible_user }}"
```

### Group Variables

#### group_vars/all.yml
```yaml
---
# Common variables for all environments
git_repo_url: "https://github.com/jbx09/ai-agentic-workflows.git"
ansible_python_interpreter: /usr/bin/python3

# SSL Configuration
ssl_cert_path: /opt/ssl/certs
ssl_renew_cron: "0 2 * * 0"  # Weekly renewal check

# Docker Configuration
docker_profiles: ["cpu"]
docker_log_driver: "json-file"
docker_log_max_size: "10m"
docker_log_max_file: "3"

# Monitoring
monitoring_enabled: true
log_retention_days: 30
```

#### group_vars/production.yml
```yaml
---
# Production-specific variables
environment: production
domain: arumai.ai
ssl_email: <EMAIL>

# Database Configuration
postgres_max_connections: 200
postgres_shared_buffers: "256MB"

# Resource Limits
docker_memory_limit: "2g"
docker_cpu_limit: "1.5"

# Security
fail2ban_enabled: true
ssh_port: 2222
```

### Templates

#### templates/.env.j2
```bash
# Environment: {{ environment }}
# Generated by Ansible on {{ ansible_date_time.iso8601 }}

# Database Configuration
POSTGRES_USER={{ vault_postgres_user }}
POSTGRES_PASSWORD={{ vault_postgres_password }}
POSTGRES_DB={{ postgres_db | default('arumai_db') }}

# N8N Configuration
N8N_ENCRYPTION_KEY={{ vault_n8n_encryption_key }}
N8N_USER_MANAGEMENT_JWT_SECRET={{ vault_n8n_jwt_secret }}

# Flowise Configuration
FLOWISE_USERNAME={{ vault_flowise_username }}
FLOWISE_PASSWORD={{ vault_flowise_password }}

# Domain Configuration
DOMAIN={{ domain }}
EMAIL={{ ssl_email }}

# Traefik Configuration
TRAEFIK_AUTH_USER={{ vault_traefik_auth_user }}

# Server Configuration
SERVER_IP={{ ansible_default_ipv4.address }}
```

## GitHub Actions CI/CD Pipeline

### Workflow Structure

```
.github/
├── workflows/
│   ├── ci.yml                 # Continuous Integration
│   ├── deploy-dev.yml         # Development deployment
│   ├── deploy-staging.yml     # Staging deployment
│   ├── deploy-prod.yml        # Production deployment
│   └── security-scan.yml      # Security scanning
├── actions/                   # Custom actions
│   └── ansible-deploy/
└── templates/                 # Issue/PR templates
```

### Main CI Workflow

#### .github/workflows/ci.yml
```yaml
name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Lint Docker Compose
        run: docker compose config
      
      - name: Lint Ansible playbooks
        uses: ansible/ansible-lint-action@v6
        with:
          path: ansible/
  
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
  
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Start services
        run: docker compose --profile cpu up -d
      
      - name: Wait for services
        run: sleep 30
      
      - name: Health check
        run: |
          curl -f http://localhost/health || exit 1
      
      - name: Stop services
        run: docker compose down

  ansible-syntax:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Ansible
        uses: ansible/ansible-action@v1
        
      - name: Syntax check
        run: |
          cd ansible
          ansible-playbook --syntax-check playbooks/site.yml
```

### Deployment Workflows

#### .github/workflows/deploy-dev.yml
```yaml
name: Deploy to Development

on:
  push:
    branches: [ develop ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: development
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Ansible
        uses: ansible/ansible-action@v1
        
      - name: Install Ansible collections
        run: ansible-galaxy collection install community.docker
        
      - name: Setup SSH key
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Deploy with Ansible
        run: |
          cd ansible
          echo "${{ secrets.ANSIBLE_VAULT_PASSWORD }}" > .vault_pass
          ansible-playbook -i inventory/development.yml playbooks/site.yml
        env:
          ANSIBLE_HOST_KEY_CHECKING: False
      
      - name: Verify deployment
        run: |
          curl -f https://dev.arumai.ai/health
      
      - name: Notify Slack
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

#### .github/workflows/deploy-prod.yml
```yaml
name: Deploy to Production

on:
  release:
    types: [published]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.release.tag_name }}
      
      - name: Manual approval
        uses: trstringer/manual-approval@v1
        with:
          secret: ${{ github.TOKEN }}
          approvers: jbx09
          minimum-approvals: 1
          issue-title: "Deploy ${{ github.event.release.tag_name }} to production"
          issue-body: |
            Please review and approve the deployment of version ${{ github.event.release.tag_name }} to production.
            
            **Changes:**
            ${{ github.event.release.body }}
      
      - name: Blue-Green deployment
        run: |
          cd ansible
          ansible-playbook -i inventory/production.yml playbooks/blue-green-deploy.yml \
            -e "release_version=${{ github.event.release.tag_name }}"
```

### Custom Actions

#### .github/actions/ansible-deploy/action.yml
```yaml
name: 'Ansible Deploy'
description: 'Deploy using Ansible playbooks'
inputs:
  environment:
    description: 'Target environment'
    required: true
  playbook:
    description: 'Playbook to run'
    required: true
    default: 'site.yml'
  vault-password:
    description: 'Ansible vault password'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Setup Ansible
      uses: ansible/ansible-action@v1
      
    - name: Install dependencies
      shell: bash
      run: |
        ansible-galaxy collection install community.docker
        ansible-galaxy collection install ansible.posix
    
    - name: Run playbook
      shell: bash
      run: |
        cd ansible
        echo "${{ inputs.vault-password }}" > .vault_pass
        ansible-playbook -i inventory/${{ inputs.environment }}.yml playbooks/${{ inputs.playbook }}
```

## Workflow Synchronization

### n8n Workflow Management

#### Export Script
```bash
#!/bin/bash
# scripts/export-n8n-workflows.sh

set -e

ENVIRONMENT=${1:-development}
N8N_URL="https://n8n.${DOMAIN}"
BACKUP_DIR="./n8n-workflows/${ENVIRONMENT}"

echo "Exporting n8n workflows from ${ENVIRONMENT}..."

# Create backup directory
mkdir -p "${BACKUP_DIR}"

# Export workflows
curl -X GET "${N8N_URL}/api/v1/workflows" \
  -H "Authorization: Bearer ${N8N_API_TOKEN}" \
  | jq '.data[]' > "${BACKUP_DIR}/workflows.json"

# Export credentials
curl -X GET "${N8N_URL}/api/v1/credentials" \
  -H "Authorization: Bearer ${N8N_API_TOKEN}" \
  | jq '.data[]' > "${BACKUP_DIR}/credentials.json"

echo "Export completed to ${BACKUP_DIR}"
```

#### Import Script
```bash
#!/bin/bash
# scripts/import-n8n-workflows.sh

set -e

SOURCE_ENV=${1:-development}
TARGET_ENV=${2:-staging}
SOURCE_DIR="./n8n-workflows/${SOURCE_ENV}"
TARGET_URL="https://n8n.${TARGET_DOMAIN}"

echo "Importing workflows from ${SOURCE_ENV} to ${TARGET_ENV}..."

# Import workflows
while IFS= read -r workflow; do
  curl -X POST "${TARGET_URL}/api/v1/workflows" \
    -H "Authorization: Bearer ${TARGET_N8N_API_TOKEN}" \
    -H "Content-Type: application/json" \
    -d "${workflow}"
done < "${SOURCE_DIR}/workflows.json"

echo "Import completed"
```

### Flowise Chatflow Management

#### Backup and Restore
```bash
#!/bin/bash
# scripts/sync-flowise-chatflows.sh

ENVIRONMENT=${1:-development}
FLOWISE_URL="https://flowise.${DOMAIN}"
BACKUP_DIR="./flowise-chatflows/${ENVIRONMENT}"

# Export chatflows
mkdir -p "${BACKUP_DIR}"
curl -X GET "${FLOWISE_URL}/api/v1/chatflows" \
  -H "Authorization: Bearer ${FLOWISE_API_TOKEN}" \
  > "${BACKUP_DIR}/chatflows.json"

echo "Flowise chatflows exported to ${BACKUP_DIR}"
```

## Monitoring & Alerting

### Prometheus Configuration

#### ansible/templates/prometheus.yml.j2
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik:8080']
  
  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
  
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

### Alert Rules

#### ansible/templates/alert_rules.yml.j2
```yaml
groups:
  - name: ai-services
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.instance }} is down"
          description: "{{ $labels.instance }} has been down for more than 2 minutes."
      
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is above 80% for more than 5 minutes."
```

## Security & Secrets Management

### Ansible Vault

#### Creating encrypted variables
```bash
# Create vault file
ansible-vault create group_vars/all/vault.yml

# Edit vault file
ansible-vault edit group_vars/all/vault.yml

# Encrypt string
ansible-vault encrypt_string 'secret_password' --name 'vault_postgres_password'
```

#### group_vars/all/vault.yml
```yaml
$ANSIBLE_VAULT;1.1;AES256
66386439653039653936656532343432626661623835663037323562343564376534653632393561
6163376238636238366261636638383765376533353163630a373036316464396136646264353165
37636335353863383036313962663035663063656166343034323832386533373662386661646137
6665316435386464340a376439353830636230616135646434373465656566373564633766636636
32313864643537313831326363313337636538653062626335636538356464646461623431663035
3837653363366165383062306636346135376636336536346338
```

### GitHub Secrets

Required secrets in GitHub repository settings:

```bash
# SSH Access
SSH_PRIVATE_KEY              # Private key for server access
SSH_KNOWN_HOSTS              # Known hosts file content

# Ansible Vault
ANSIBLE_VAULT_PASSWORD       # Vault password for encrypted variables

# API Tokens
N8N_API_TOKEN               # n8n API token for workflow sync
FLOWISE_API_TOKEN           # Flowise API token for chatflow sync

# Notifications
SLACK_WEBHOOK               # Slack webhook for deployment notifications
```

## Usage Instructions

### Initial Setup

1. **Prepare Ansible Vault**
   ```bash
   cd ansible
   echo "your_vault_password" > .vault_pass
   chmod 600 .vault_pass
   ansible-vault create group_vars/all/vault.yml
   ```

2. **Configure Inventory**
   ```bash
   # Edit inventory files with your server details
   vi inventory/development.yml
   vi inventory/production.yml
   ```

3. **Test Connection**
   ```bash
   ansible all -i inventory/development.yml -m ping
   ```

### Deployment Commands

#### Manual Deployment
```bash
# Deploy to development
ansible-playbook -i inventory/development.yml playbooks/site.yml

# Deploy to production
ansible-playbook -i inventory/production.yml playbooks/site.yml

# Deploy specific components
ansible-playbook -i inventory/production.yml playbooks/ssl-setup.yml
```

#### Workflow Synchronization
```bash
# Export from development
./scripts/export-n8n-workflows.sh development

# Import to staging
./scripts/import-n8n-workflows.sh development staging
```

### GitHub Actions Triggers

1. **Automatic Deployments**
   - Push to `develop` → Deploy to development
   - Push to `main` → Deploy to staging
   - Create release → Deploy to production (with approval)

2. **Manual Deployments**
   - Use "workflow_dispatch" in GitHub Actions tab
   - Select environment and branch
   - Monitor deployment in Actions tab

## Best Practices

### Development Workflow

1. **Feature Development**
   ```bash
   git checkout -b feature/new-workflow
   # Develop and test locally
   git push origin feature/new-workflow
   # Create PR to develop branch
   ```

2. **Testing Process**
   - Automated tests run on PR
   - Deploy to development environment
   - Manual testing and validation
   - Merge to develop branch

3. **Release Process**
   - Merge develop to main
   - Deploy to staging
   - Create release tag
   - Deploy to production with approval

### Security Considerations

1. **Secrets Management**
   - Use Ansible Vault for sensitive data
   - Rotate secrets regularly
   - Use least privilege principle

2. **Access Control**
   - SSH key-based authentication
   - Limited sudo access
   - Regular security updates

3. **Monitoring**
   - Log all deployment activities
   - Monitor service health
   - Alert on failures

## Troubleshooting

### Common Issues

1. **Ansible Connection Failures**
   ```bash
   # Test connectivity
   ansible all -i inventory/development.yml -m ping
   
   # Debug SSH issues
   ansible all -i inventory/development.yml -m setup -vvv
   ```

2. **Docker Deployment Issues**
   ```bash
   # Check Docker daemon
   ansible all -i inventory/development.yml -m shell -a "docker ps"
   
   # View service logs
   ansible all -i inventory/development.yml -m shell -a "docker compose logs"
   ```

3. **GitHub Actions Failures**
   - Check Actions tab for detailed logs
   - Verify secrets configuration
   - Test Ansible playbooks locally

### Debug Commands

```bash
# Dry run deployment
ansible-playbook -i inventory/development.yml playbooks/site.yml --check

# Run specific tags
ansible-playbook -i inventory/development.yml playbooks/site.yml --tags ssl

# Debug mode
ansible-playbook -i inventory/development.yml playbooks/site.yml -vvv
```

## Next Steps

1. **Implement Ansible playbooks** for current manual setup
2. **Create GitHub Actions workflows** for automated CI/CD
3. **Set up monitoring and alerting** with Prometheus/Grafana
4. **Configure workflow synchronization** between environments
5. **Implement blue-green deployment** for production
6. **Add automated testing** for workflows and services

This automation framework will significantly improve deployment reliability, reduce manual effort, and enable rapid iteration while maintaining security and quality standards. 