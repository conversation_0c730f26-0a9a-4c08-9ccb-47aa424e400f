# Server Deployment Guide

This guide covers deploying the AI stack to a remote server VM with manually obtained certificates.

## Problem Overview

The errors you're seeing indicate:
1. **Certificate files not accessible** - `/etc/letsencrypt/live/dev.arumai.ai/` doesn't exist in containers
2. **Old acme-dns containers** - Leftover configurations causing router errors

## Quick Fix Steps

### 1. Copy Certificates from Server to Local

On your **server VM (*************)**:
```bash
# Copy certificates to temporary location
sudo cp /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem /tmp/
sudo cp /etc/letsencrypt/live/dev.arumai.ai/privkey.pem /tmp/
sudo chmod 644 /tmp/fullchain.pem /tmp/privkey.pem
```

On your **local machine**:
```bash
# Copy certificates from server
scp user@*************:/tmp/fullchain.pem ./certs/
scp user@*************:/tmp/privkey.pem ./certs/

# Or run the helper script
./scripts/copy-certs-from-server.sh
```

### 2. Clean Up Old Containers on Server

On your **server VM**:
```bash
# Stop current services
docker-compose down

# Clean up old acme-dns containers
docker stop acme-dns 2>/dev/null || true
docker rm acme-dns 2>/dev/null || true
docker volume prune -f
docker network prune -f
```

### 3. Deploy Updated Configuration

Copy your updated files to the server:
```bash
# Copy entire project to server
rsync -av --exclude='.git' ./ user@*************:~/ai-agentic-workflow/

# Or copy specific files
scp docker-compose.yml user@*************:~/ai-agentic-workflow/
scp -r certs/ user@*************:~/ai-agentic-workflow/
scp .env user@*************:~/ai-agentic-workflow/
```

### 4. Start Services on Server

On your **server VM**:
```bash
cd ~/ai-agentic-workflow

# Verify certificates exist
ls -la ./certs/
# Should show: fullchain.pem, privkey.pem, tls.yml

# Start services
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs traefik
```

## Verification

### Check Certificate Loading
```bash
# On server, check Traefik logs
docker-compose logs traefik | grep -i certificate
docker-compose logs traefik | grep -i tls

# Should show certificate loaded successfully
```

### Test HTTPS Access
```bash
# Test from server
curl -k https://traefik.dev.arumai.ai

# Test from outside
curl -k https://*************
```

### Access Services
- **Traefik Dashboard**: https://traefik.dev.arumai.ai
- **n8n**: https://n8n.dev.arumai.ai
- **Flowise**: https://flowise.dev.arumai.ai
- **Open WebUI**: https://chat.dev.arumai.ai

## Troubleshooting

### Certificate Permission Issues
```bash
# Fix permissions on server
chmod 644 ./certs/fullchain.pem ./certs/privkey.pem

# Check file contents
head -5 ./certs/fullchain.pem
# Should start with: -----BEGIN CERTIFICATE-----
```

### Old Router Errors
If you still see `acmedns-dns-tcp@docker` errors:
```bash
# Complete cleanup
docker-compose down --remove-orphans
docker system prune -f
docker-compose up -d
```

### DNS Issues
Ensure DNS records point to your server:
```bash
# Check DNS resolution
dig traefik.dev.arumai.ai
dig n8n.dev.arumai.ai

# Should return: *************
```

## File Structure on Server

Your server should have:
```
~/ai-agentic-workflow/
├── docker-compose.yml
├── .env
├── certs/
│   ├── tls.yml
│   ├── fullchain.pem
│   └── privkey.pem
└── scripts/
```

## Common Issues

**Error: "failed to find any PEM data"**
- Certificates not copied correctly
- Wrong file permissions
- Empty certificate files

**Error: "EntryPoint doesn't exist"**
- Old acme-dns containers still running
- Need to clean up with `docker-compose down --remove-orphans`

**Error: "certificate resolver acmedns"**
- Old docker containers with wrong labels
- Run cleanup script

## Security Notes

- Certificates are copied with read-only permissions
- Private keys only accessible to Traefik container
- Use proper SSH key authentication for server access
- Consider using a deployment tool for production 