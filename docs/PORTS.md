# Port Configuration

This document explains the port assignments for all services in the AI stack.

## External Ports (Exposed to Host)

| Port | Service | Purpose | Access |
|------|---------|---------|---------|
| 80 | Traefik | HTTP (redirects to HTTPS) | Public |
| 443 | Traefik | HTTPS | Public |
| 5432 | PostgreSQL | Database | Local only |
| 6333 | Qdrant | Vector database | Local + HTTPS |
| 11434 | Ollama | LLM API | Local + HTTPS |

## Service Access URLs

### HTTPS (via Traefik with manual certificates)
- **n8n**: https://n8n.dev.arumai.ai
- **Flowise**: https://flowise.dev.arumai.ai  
- **Open WebUI**: https://chat.dev.arumai.ai
- **Qdrant**: https://vector.dev.arumai.ai
- **Ollama**: https://ai.dev.arumai.ai
- **Traefik Dashboard**: https://traefik.dev.arumai.ai

### Local Access (HTTP)
- **PostgreSQL**: localhost:5432
- **Qdrant**: localhost:6333
- **Ollama**: localhost:11434

## Architecture

### Simplified Setup with Manual Certificates

```
Internet → Traefik (80/443) → Services
                ↑
        Manual SSL Certificates
        (from /etc/letsencrypt)
```

**Benefits:**
- Only 2 ports exposed (80, 443)
- No DNS challenge complexity
- Immediate SSL with existing certificates
- Simplified configuration

## Port Conflicts & Solutions

### Common Conflicts

**Port 80/443 (HTTP/HTTPS)**  
- May conflict with existing web servers (Apache, Nginx)
- Solution: Stop conflicting services or change ports

**Port 5432 (PostgreSQL)**
- Conflicts with existing PostgreSQL installations
- Solution: Stop existing PostgreSQL or change port mapping

### Checking Port Availability

Run our port checker script:
```bash
./scripts/check-ports.sh
```

Or check individual ports:
```bash
netstat -tlnp | grep :PORT_NUMBER
sudo lsof -i :PORT_NUMBER
```

## Internal Network Communication

All services communicate internally via the `ainetwork` Docker network:
- Services can reach each other by container name
- No external port exposure needed for internal communication
- Example: n8n connects to postgres via `postgres:5432`

## Security Notes

- Only necessary ports are exposed to the host
- HTTPS is enforced for all web services via Traefik
- Database ports (5432) are exposed locally for development but protected by network isolation
- SSL certificates are mounted read-only into Traefik
- All traffic encrypted end-to-end

## Certificate Management

### Manual Certificate Setup
- Certificates loaded from `/etc/letsencrypt/live/dev.arumai.ai/`
- Traefik uses file provider for certificate loading
- No automatic renewal (manual `certbot renew` required)
- Restart Traefik after certificate renewal

### Certificate Files Required
```
/etc/letsencrypt/live/dev.arumai.ai/
├── fullchain.pem
└── privkey.pem
``` 