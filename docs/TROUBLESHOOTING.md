# Troubleshooting Guide

This document covers common issues and their solutions.

## SSL Certificate Issues

### Error: "some credentials information are missing: ACME_DNS_API_BASE"

**Problem**: <PERSON><PERSON><PERSON><PERSON> can't connect to acme-dns because required environment variables are missing.

**Solution**: ✅ **Fixed in current config**
```yaml
environment:
  - ACME_DNS_CONFIG_FILE=/etc/traefik/acme-dns.json
  - ACME_DNS_API_BASE=http://acme-dns:80
  - ACME_DNS_STORAGE_PATH=/etc/traefik/acme-dns.json
```

### Testing vs Production Certificates

**Staging Environment** (for testing):
```yaml
- "--certificatesresolvers.acmedns.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory"
```

**Production Environment** (remove the line above):
```yaml
# Uses production Let's Encrypt by default
```

**Why use staging first?**
- No rate limits during testing
- Avoid hitting production rate limits
- Certificates will show "Fake LE Intermediate" (expected)

## DNS Configuration Issues

### Error: DNS propagation timeout

**Check DNS setup**:
```bash
# Check if DNS records exist
dig _acme-challenge.dev.arumai.ai CNAME
dig auth.dev.arumai.ai A

# Expected results:
# _acme-challenge.dev.arumai.ai -> a72dff7d-cfe6-4331-b8a8-efc23763b055.auth.acme-dns.io
# auth.dev.arumai.ai -> YOUR_SERVER_IP
```

### DNS Records Not Working

**Required DNS Records**:
```
# GoDaddy DNS Records
Type: A
Name: auth.dev
Value: YOUR_SERVER_IP
TTL: 600

Type: CNAME  
Name: _acme-challenge.dev
Value: a72dff7d-cfe6-4331-b8a8-efc23763b055.auth.acme-dns.io
TTL: 600

Type: CNAME
Name: _acme-challenge
Value: a72dff7d-cfe6-4331-b8a8-efc23763b055.auth.acme-dns.io  
TTL: 600
```

## Service Connection Issues

### acme-dns Registration Failed

**Check service status**:
```bash
docker compose ps
docker compose logs acme-dns
docker compose logs traefik
```

**Test acme-dns connectivity**:
```bash
# Test internal connectivity
docker compose exec traefik curl http://acme-dns:80

# Test external (if DNS is set up)
curl http://auth.dev.arumai.ai
```

### Traefik Dashboard Not Accessible

**Check Traefik auth configuration**:
```bash
# Generate basic auth string
htpasswd -nb admin yourpassword
# Add to .env file: TRAEFIK_AUTH_USER=admin:$apr1$...
```

## Port Conflicts

### Port 53 Already in Use

**Common cause**: systemd-resolved
```bash
# Check what's using port 53
sudo netstat -tlnp | grep :53
sudo lsof -i :53

# Disable systemd-resolved temporarily
sudo systemctl stop systemd-resolved
sudo systemctl disable systemd-resolved

# Use custom DNS
echo "nameserver 8.8.8.8" | sudo tee /etc/resolv.conf
```

### Port 80/443 Already in Use

**Check and stop conflicting services**:
```bash
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# Stop Apache/Nginx if running
sudo systemctl stop apache2
sudo systemctl stop nginx
```

## Certificate Validation

### Check Certificate Status

**Via Traefik Dashboard**:
- Go to https://traefik.dev.arumai.ai
- Check "HTTP > Routers" section
- Look for TLS status

**Via Command Line**:
```bash
# Check certificate details
openssl s_client -connect traefik.dev.arumai.ai:443 -servername traefik.dev.arumai.ai

# Expected output should show:
# - Issuer: (STAGING) Fake LE Intermediate (for staging)
# - Issuer: Let's Encrypt Authority (for production)
```

## Log Analysis

### Enable Debug Logging

Add to Traefik command section:
```yaml
- "--log.level=DEBUG"
- "--certificatesresolvers.acmedns.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory"
```

### Key Log Messages

**Successful certificate generation**:
```
Certificate obtained successfully
```

**DNS challenge success**:
```
Cleaning DNS TXT record
```

**Registration success**:
```
acme-dns: Credentials saved
```

## Environment Variables

### Required Variables in .env

```bash
# Basic setup
DOMAIN=dev.arumai.ai
EMAIL=<EMAIL>
SERVER_IP=your.server.ip

# Traefik auth (generate with htpasswd)
TRAEFIK_AUTH_USER=admin:$apr1$...

# Database
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=n8n

# Services
FLOWISE_USERNAME=admin
FLOWISE_PASSWORD=your_secure_password
N8N_ENCRYPTION_KEY=your_32_char_encryption_key
N8N_USER_MANAGEMENT_JWT_SECRET=your_jwt_secret
```

## Quick Diagnostics

**Run full diagnostic**:
```bash
# Check all ports
./scripts/check-ports.sh

# Check container status
docker compose ps

# Check logs for errors
docker compose logs --tail=50 traefik
docker compose logs --tail=50 acme-dns

# Test DNS resolution
dig auth.dev.arumai.ai
dig _acme-challenge.dev.arumai.ai CNAME
``` 