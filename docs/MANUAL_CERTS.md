# Manual SSL Certificate Setup

This guide covers using Traefik with manually obtained SSL certificates (like from Let's Encrypt).

## Overview

Instead of using automatic certificate generation, this setup uses pre-obtained certificates from Let's Encrypt or any other Certificate Authority.

## Benefits

✅ **No complex ACME/DNS challenges**
✅ **Works with any certificate provider**
✅ **Simpler Traefik configuration**
✅ **No dependency on acme-dns**
✅ **Immediate SSL without waiting for certificate generation**

## Prerequisites

- SSL certificates already obtained (wildcard or individual domain certificates)
- Certificate files accessible on the server
- DNS records pointing to your server

## Certificate Location

Your certificates should be located at:
```
/etc/letsencrypt/live/dev.arumai.ai/
├── fullchain.pem    # Full certificate chain
└── privkey.pem      # Private key
```

## Configuration

### 1. Traefik File Provider

The setup uses Traefik's file provider to load certificates:

```yaml
# File provider for certificates
- "--providers.file.filename=/config/tls.yml"
- "--providers.file.watch=true"
```

### 2. Certificate Configuration

The `certs/tls.yml` file defines the certificate locations:

```yaml
tls:
  certificates:
    - certFile: /certs/fullchain.pem
      keyFile: /certs/privkey.pem
      stores:
        - default
  stores:
    default:
      defaultCertificate:
        certFile: /certs/fullchain.pem
        keyFile: /certs/privkey.pem
```

### 3. Volume Mounts

Docker mounts the certificates into Traefik:

```yaml
volumes:
  - ./certs/tls.yml:/config/tls.yml:ro
  - /etc/letsencrypt/live/dev.arumai.ai:/certs:ro
```

## Setup Process

### 1. Run Setup Script

```bash
./scripts/setup-manual-certs.sh
```

This script will:
- Check if certificates exist
- Verify configuration
- Create .env file if needed
- Check permissions

### 2. Configure Environment

Edit `.env` file with your settings:

```bash
DOMAIN=dev.arumai.ai
EMAIL=<EMAIL>
SERVER_IP=your.server.ip

# Generate with: htpasswd -nb admin yourpassword
TRAEFIK_AUTH_USER=admin:$apr1$...

# Database settings
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=n8n

# Service credentials
FLOWISE_USERNAME=admin
FLOWISE_PASSWORD=your_secure_password
N8N_ENCRYPTION_KEY=your_32_char_encryption_key
N8N_USER_MANAGEMENT_JWT_SECRET=your_jwt_secret
```

### 3. Set DNS Records

Point all subdomains to your server IP:

```
n8n.dev.arumai.ai       -> YOUR_SERVER_IP
flowise.dev.arumai.ai   -> YOUR_SERVER_IP
chat.dev.arumai.ai      -> YOUR_SERVER_IP
vector.dev.arumai.ai    -> YOUR_SERVER_IP
ai.dev.arumai.ai        -> YOUR_SERVER_IP
traefik.dev.arumai.ai   -> YOUR_SERVER_IP
```

### 4. Start Services

```bash
docker-compose up -d
```

### 5. Verify Setup

```bash
# Check container status
docker-compose ps

# Check Traefik logs
docker-compose logs traefik

# Test HTTPS access
curl -k https://traefik.dev.arumai.ai
```

## Service URLs

Once running, access your services at:

- **Traefik Dashboard**: https://traefik.dev.arumai.ai
- **n8n**: https://n8n.dev.arumai.ai
- **Flowise**: https://flowise.dev.arumai.ai
- **Open WebUI**: https://chat.dev.arumai.ai
- **Qdrant**: https://vector.dev.arumai.ai
- **Ollama**: https://ai.dev.arumai.ai

## Certificate Renewal

### Manual Renewal

When certificates expire, renew them manually:

```bash
# Renew with certbot
sudo certbot renew

# Restart Traefik to reload certificates
docker-compose restart traefik
```

### Automatic Renewal (Optional)

Add a cron job for automatic renewal:

```bash
# Edit crontab
sudo crontab -e

# Add renewal job (runs daily at 2 AM)
0 2 * * * certbot renew --quiet && docker-compose -f /path/to/docker-compose.yml restart traefik
```

## Troubleshooting

### Certificate Permission Issues

If Traefik can't read certificates:

```bash
# Fix permissions
sudo chmod +r /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem
sudo chmod +r /etc/letsencrypt/live/dev.arumai.ai/privkey.pem

# Or copy to local directory
sudo cp /etc/letsencrypt/live/dev.arumai.ai/*.pem ./certs/
sudo chown $(whoami):$(whoami) ./certs/*.pem

# Update docker-compose.yml volume mount
# - ./certs:/certs:ro
```

### Certificate Not Loading

Check Traefik logs:

```bash
docker-compose logs traefik | grep -i tls
docker-compose logs traefik | grep -i certificate
```

Common issues:
- Incorrect file paths
- Permission denied
- Invalid certificate format

### Service Not Accessible

1. **Check DNS**: `dig service.dev.arumai.ai`
2. **Check ports**: `netstat -tlnp | grep :443`
3. **Check Traefik routes**: Visit Traefik dashboard
4. **Check service health**: `docker-compose ps`

## Security Notes

- Certificates are mounted read-only
- Private keys are only accessible to Traefik container
- HTTPS is enforced for all services
- Security headers are applied via middleware

## Comparison with ACME Setup

| Feature | Manual Certs | ACME Auto |
|---------|--------------|-----------|
| Setup Complexity | Simple | Complex |
| Certificate Renewal | Manual | Automatic |
| DNS Dependencies | Basic | Advanced |
| Suitable for | Production/Testing | Production |
| Maintenance | Low | Very Low | 