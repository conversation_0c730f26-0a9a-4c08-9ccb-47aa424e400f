# Database Configuration (for n8n only)
POSTGRES_USER=arumai
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=arumai_db

# N8N Configuration
N8N_ENCRYPTION_KEY=your_n8n_encryption_key_here
N8N_USER_MANAGEMENT_JWT_SECRET=your_jwt_secret_here

# Flowise Configuration
FLOWISE_USERNAME=admin
FLOWISE_PASSWORD=your_flowise_password_here

# Domain Configuration
DOMAIN=dev.arumai.ai
EMAIL=<EMAIL>

# Traefik Dashboard Authentication (generate with: htpasswd -nb admin your_password)
TRAEFIK_AUTH_USER=admin:$2y$10$example_hash_here

# Server Configuration
SERVER_IP=your_server_ip_here 