user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Hide nginx version
    server_tokens off;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Upstream definitions
    upstream n8n {
        server n8n:5678;
    }

    upstream flowise {
        server flowise:3001;
    }

    upstream open-webui {
        server open-webui:8080;
    }

    upstream qdrant {
        server qdrant:6333;
    }

    upstream ollama {
        server ollama-cpu:11434;
    }

    # Include additional configurations
    include /etc/nginx/conf.d/*.conf;
} 