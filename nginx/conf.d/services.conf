# N8N - n8n.dev.arumai.ai
server {
    listen 443 ssl http2;
    server_name n8n.dev.arumai.ai;

    ssl_certificate /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dev.arumai.ai/privkey.pem;

    location / {
        proxy_pass http://n8n;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Flowise - flowise.dev.arumai.ai
server {
    listen 443 ssl http2;
    server_name flowise.dev.arumai.ai;

    ssl_certificate /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dev.arumai.ai/privkey.pem;

    location / {
        proxy_pass http://flowise;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Larger body size for file uploads
        client_max_body_size 100M;
    }
}

# Open WebUI - chat.dev.arumai.ai
server {
    listen 443 ssl http2;
    server_name chat.dev.arumai.ai;

    ssl_certificate /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dev.arumai.ai/privkey.pem;

    location / {
        proxy_pass http://open-webui;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # WebSocket support for real-time features
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Larger body size for file uploads
        client_max_body_size 100M;
    }
}

# Qdrant - vector.dev.arumai.ai
server {
    listen 443 ssl http2;
    server_name vector.dev.arumai.ai;

    ssl_certificate /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dev.arumai.ai/privkey.pem;

    location / {
        proxy_pass http://qdrant;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # API timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# Ollama - ai.dev.arumai.ai
server {
    listen 443 ssl http2;
    server_name ai.dev.arumai.ai;

    ssl_certificate /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dev.arumai.ai/privkey.pem;

    location / {
        proxy_pass http://ollama;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Longer timeouts for AI model responses
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # Larger body size for model uploads
        client_max_body_size 10G;
    }
} 