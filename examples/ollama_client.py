"""
Example client for interacting with <PERSON><PERSON><PERSON> through the containerized setup
"""

import os
import requests
import json
from typing import List, Dict, Any

class OllamaClient:
    def __init__(self, base_url: str = None):
        self.base_url = base_url or os.getenv("OLLAMA_API_URL", "http://ollama:11434/v1/chat/completions")
        self.models_url = self.base_url.replace("/v1/chat/completions", "/api/tags")
    
    def chat(self, messages: List[Dict[str, str]], model: str = "llama3.1", **kwargs) -> str:
        """
        Send a chat request to Ollama
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            model: Model name to use
            **kwargs: Additional parameters like temperature, max_tokens
        
        Returns:
            Response content as string
        """
        payload = {
            "model": model,
            "messages": messages,
            "stream": False,
            **kwargs
        }
        
        try:
            response = requests.post(
                self.base_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except requests.exceptions.RequestException as e:
            print(f"Error communicating with Ollama: {e}")
            return None
    
    def list_models(self) -> List[Dict[str, Any]]:
        """List available models"""
        try:
            response = requests.get(self.models_url, timeout=10)
            response.raise_for_status()
            return response.json().get("models", [])
        except requests.exceptions.RequestException as e:
            print(f"Error fetching models: {e}")
            return []
    
    def simple_chat(self, prompt: str, model: str = "llama3.1", **kwargs) -> str:
        """Simple chat with a single prompt"""
        messages = [{"role": "user", "content": prompt}]
        return self.chat(messages, model, **kwargs)

def main():
    """Example usage"""
    client = OllamaClient()
    
    # List available models
    print("Available models:")
    models = client.list_models()
    for model in models:
        print(f"- {model.get('name', 'Unknown')}")
    
    # Simple chat example
    print("\n" + "="*50)
    print("Simple Chat Example")
    print("="*50)
    
    response = client.simple_chat(
        "Explain what Docker containers are in simple terms.",
        temperature=0.7,
        max_tokens=200
    )
    
    if response:
        print(f"Response: {response}")
    else:
        print("Failed to get response from Ollama")
    
    # Multi-turn conversation example
    print("\n" + "="*50)
    print("Multi-turn Conversation Example")
    print("="*50)
    
    messages = [
        {"role": "user", "content": "What is artificial intelligence?"},
        {"role": "assistant", "content": "Artificial intelligence (AI) is a branch of computer science that aims to create machines capable of performing tasks that typically require human intelligence."},
        {"role": "user", "content": "Can you give me a practical example?"}
    ]
    
    response = client.chat(messages, temperature=0.5)
    if response:
        print(f"Response: {response}")
    else:
        print("Failed to get response from Ollama")

if __name__ == "__main__":
    main()
