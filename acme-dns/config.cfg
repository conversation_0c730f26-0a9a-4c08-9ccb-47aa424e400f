[general]
# DNS interface
listen = "0.0.0.0:53"
# HTTP API interface  
protocol = "both4"
# Domain name for acme-dns instance
domain = "auth.dev.arumai.ai"
# Zone name server
nsname = "auth.dev.arumai.ai"
# Admin email for SOA
nsadmin = "admin.dev.arumai.ai"
# Predefined records served in addition to the TXT
records = [
    # Domain pointing to the public IP of this server
    "auth.dev.arumai.ai. A ***********",
    # Nameserver record
    "auth.dev.arumai.ai. NS auth.dev.arumai.ai.",
]
# debug messages from CORS etc
debug = false

[database]
# Database engine to use, sqlite3 or postgres
engine = "sqlite3"
# Connection string, filename for sqlite3 and ********************************************* for postgres
connection = "/var/lib/acme-dns/acme-dns.db"
# Connection string for test database
connection_test = "/var/lib/acme-dns/acme-dns-test.db"

[api]
# listen ip and port for the HTTP API
api_domain = ""
port = "80"
# disable registration endpoint
disable_registration = false
# optional HTTP header to check for API calls
header_password = ""
# CORS enabled domains
corsorigins = [
    "*"
]
# Use HTTP header to get the client ip
use_header = false
# Header name to pull the ip address / list of ip addresses from
header_name = "X-Forwarded-For"

[logconfig]
# logging level: "error", "warning", "info" or "debug"
loglevel = "info"
# possible values: stdout, TODO file support
logtype = "stdout"
# format, either "json" or "text"
logformat = "text" 