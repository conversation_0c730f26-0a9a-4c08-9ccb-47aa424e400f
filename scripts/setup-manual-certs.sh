#!/bin/bash

# Manual Certificate Setup Script
# This script helps configure Traefik to use manually obtained certificates

set -e

echo "🔐 Setting up manual SSL certificates with Traefik..."

# Check if certificates exist
CERT_PATH="/etc/letsencrypt/live/dev.arumai.ai"
if [ ! -f "$CERT_PATH/fullchain.pem" ] || [ ! -f "$CERT_PATH/privkey.pem" ]; then
    echo "❌ Error: Certificates not found at $CERT_PATH"
    echo "Expected files:"
    echo "  - $CERT_PATH/fullchain.pem"
    echo "  - $CERT_PATH/privkey.pem"
    echo ""
    echo "If your certificates are in a different location, please update the docker-compose.yml volume mount."
    exit 1
fi

echo "✅ Found certificates at $CERT_PATH"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file from template..."
    cp env.example .env
    echo ""
    echo "⚠️  Please edit .env file with your configuration:"
    echo "   - DOMAIN=dev.arumai.ai"
    echo "   - EMAIL=<EMAIL>"
    echo "   - SERVER_IP=your.server.ip"
    echo "   - Generate TRAEFIK_AUTH_USER with: htpasswd -nb admin yourpassword"
    echo ""
    echo "Then run this script again."
    exit 1
fi

source .env

echo "📋 Configuration check:"
echo "   Domain: ${DOMAIN}"
echo "   Email: ${EMAIL}"
echo "   Server IP: ${SERVER_IP}"

if [ -z "$TRAEFIK_AUTH_USER" ]; then
    echo ""
    echo "⚠️  TRAEFIK_AUTH_USER not set in .env file"
    echo "Generate it with: htpasswd -nb admin yourpassword"
    echo "Then add the output to your .env file as TRAEFIK_AUTH_USER=..."
fi

echo ""
echo "🔍 Checking certificate permissions..."
sudo chmod +r "$CERT_PATH/fullchain.pem" "$CERT_PATH/privkey.pem" 2>/dev/null || {
    echo "⚠️  Cannot read certificates. You may need to run Traefik with proper permissions."
    echo "   Consider copying certificates to ./certs/ directory with proper permissions."
}

echo ""
echo "🚀 Ready to start services!"
echo ""
echo "Next steps:"
echo "1. Ensure DNS records point to your server:"
echo "   - n8n.dev.arumai.ai -> ${SERVER_IP}"
echo "   - flowise.dev.arumai.ai -> ${SERVER_IP}"
echo "   - chat.dev.arumai.ai -> ${SERVER_IP}"
echo "   - vector.dev.arumai.ai -> ${SERVER_IP}"
echo "   - ai.dev.arumai.ai -> ${SERVER_IP}"
echo "   - traefik.dev.arumai.ai -> ${SERVER_IP}"
echo ""
echo "2. Start the services:"
echo "   docker-compose up -d"
echo ""
echo "3. Check logs:"
echo "   docker-compose logs -f traefik"
echo ""
echo "4. Access services:"
echo "   - Traefik Dashboard: https://traefik.${DOMAIN}"
echo "   - n8n: https://n8n.${DOMAIN}"
echo "   - Flowise: https://flowise.${DOMAIN}"
echo "   - Open WebUI: https://chat.${DOMAIN}"
echo ""
echo "✨ No certificate renewal needed - your certificates are ready!" 