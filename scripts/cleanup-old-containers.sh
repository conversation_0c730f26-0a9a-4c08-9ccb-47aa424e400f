#!/bin/bash

# Cleanup old containers script
# This script removes leftover acme-dns containers and networks

set -e

echo "🧹 Cleaning up old acme-dns containers..."

# Stop and remove any acme-dns related containers
echo "Stopping old containers..."
docker stop acme-dns 2>/dev/null || echo "No acme-dns container to stop"
docker rm acme-dns 2>/dev/null || echo "No acme-dns container to remove"

# Remove any old acme-dns images
echo "Cleaning up old images..."
docker rmi joohoi/acme-dns:latest 2>/dev/null || echo "No acme-dns image to remove"

# Clean up any orphaned volumes
echo "Cleaning up unused volumes..."
docker volume prune -f

# Clean up unused networks
echo "Cleaning up unused networks..."
docker network prune -f

echo ""
echo "✅ Cleanup complete!"
echo ""
echo "Next steps:"
echo "1. Copy your certificates to ./certs/ directory"
echo "2. Run: docker-compose up -d"
echo "3. Check logs: docker-compose logs traefik" 