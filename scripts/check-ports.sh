#!/bin/bash

# Port Checker Script
# This script checks if the required ports are available

echo "🔍 Checking port availability for AI services setup..."
echo ""

# Ports used by the services
PORTS=(
    "80:Traefik HTTP"
    "443:Traefik HTTPS" 
    "53:Traefik DNS (for acme-dns challenges)"
    "5432:PostgreSQL"
    "6333:Qdrant"
    "11434:Ollama"
)

echo "📋 Checking required ports..."
echo ""

CONFLICTS=0

for port_info in "${PORTS[@]}"; do
    port=$(echo "$port_info" | cut -d: -f1)
    service=$(echo "$port_info" | cut -d: -f2)
    
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        echo "❌ Port $port ($service) - IN USE"
        echo "   Process using port $port:"
        netstat -tlnp 2>/dev/null | grep ":$port " | head -1
        CONFLICTS=$((CONFLICTS + 1))
        echo ""
    else
        echo "✅ Port $port ($service) - Available"
    fi
done

echo ""

if [ $CONFLICTS -eq 0 ]; then
    echo "🎉 All ports are available! You can proceed with the setup."
else
    echo "⚠️  Found $CONFLICTS port conflict(s)."
    echo ""
    echo "🔧 Solutions:"
    echo "1. Stop the conflicting services"
    echo "2. Change the port mapping in docker-compose.yml"
    echo "3. Use 'sudo lsof -i :PORT_NUMBER' to find what's using a specific port"
    echo ""
    echo "⚠️  Note: Port 53 conflicts are common due to system DNS services."
    echo "   You may need to disable systemd-resolved or use alternative ports."
fi

echo ""
echo "💡 To check a specific port: netstat -tlnp | grep PORT_NUMBER"
echo ""
echo "🏗️  Architecture: All services route through Traefik (single entry point)" 