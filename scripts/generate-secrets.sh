#!/bin/bash

# Generate Secrets Script
# This script generates the required encryption keys and passwords for the setup

echo "🔐 Generating secrets for your AI services setup..."
echo ""

echo "📋 Copy these values to your .env file:"
echo ""

echo "# N8N Configuration"
echo "N8N_ENCRYPTION_KEY=$(openssl rand -base64 32)"
echo "N8N_USER_MANAGEMENT_JWT_SECRET=$(openssl rand -base64 32)"
echo ""

echo "# Database Configuration"
echo "POSTGRES_PASSWORD=$(openssl rand -base64 16 | tr -d '=+/')"
echo ""

echo "# Flowise Configuration"
echo "FLOWISE_PASSWORD=$(openssl rand -base64 12 | tr -d '=+/')"
echo ""

echo "🔑 To generate Traefik dashboard authentication:"
echo "Run: htpasswd -nb admin your_chosen_password"
echo ""

echo "💡 Don't forget to set your actual values for:"
echo "- DOMAIN=dev.arumai.ai"
echo "- EMAIL=<EMAIL>" 
echo "- SERVER_IP=your_actual_server_ip"
echo "- TRAEFIK_AUTH_USER=admin:\$2y\$10\$your_bcrypt_hash" 