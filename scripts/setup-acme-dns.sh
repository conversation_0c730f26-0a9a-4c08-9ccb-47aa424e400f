#!/bin/bash

# ACME-DNS Setup Script for Traefik + Let's Encrypt
# This script sets up acme-dns for wildcard SSL certificates

set -e

# Load environment variables
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    echo "Please copy env.example to .env and configure it first"
    exit 1
fi

source .env

echo "🔧 Setting up acme-dns for wildcard SSL certificates"

# Replace placeholders in acme-dns config
echo "📝 Configuring acme-dns with your server IP..."
sed -i.bak "s/YOUR_SERVER_IP/${SERVER_IP}/g" ./acme-dns/config.cfg

echo "🚀 Starting acme-dns service (routed through Traefik)..."
docker compose up -d acme-dns

# Wait for services to be ready
echo "⏳ Waiting for acme-dns to be ready..."
sleep 15

echo "📋 Registering domain with acme-dns..."

# Register domain with acme-dns (using HTTP through Traefik on port 80)
# This will work both locally (if you have auth.${DOMAIN} pointing to localhost)
# or via the actual domain if DNS is set up
REGISTRATION_RESPONSE=$(curl -s -X POST http://localhost:80 \
    -H "Host: auth.${DOMAIN}" \
    -H "Content-Type: application/json" \
    -d '{}' /register 2>/dev/null || \
    curl -s -X POST http://${SERVER_IP}:80/register \
    -H "Host: auth.${DOMAIN}" \
    -H "Content-Type: application/json" \
    -d '{}' 2>/dev/null)

if [[ $? -ne 0 || -z "$REGISTRATION_RESPONSE" ]]; then
    echo "❌ Error: Failed to register with acme-dns"
    echo "Make sure:"
    echo "1. acme-dns and traefik are running: docker compose ps"
    echo "2. Port 80 is accessible"
    echo "3. DNS points to your server (or use /etc/hosts for testing)"
    echo ""
    echo "💡 Alternative: Register after full deployment using:"
    echo "   curl -X POST https://auth.${DOMAIN}/register"
    exit 1
fi

echo "✅ Registration successful!"
echo "📋 Registration details:"
echo "$REGISTRATION_RESPONSE" | jq '.'

# Extract values from registration response
SUBDOMAIN=$(echo "$REGISTRATION_RESPONSE" | jq -r '.subdomain')
USERNAME=$(echo "$REGISTRATION_RESPONSE" | jq -r '.username')
PASSWORD=$(echo "$REGISTRATION_RESPONSE" | jq -r '.password')
FULLDOMAIN=$(echo "$REGISTRATION_RESPONSE" | jq -r '.fulldomain')

# Create traefik directory if it doesn't exist
echo "📁 Creating traefik directory..."
mkdir -p ./traefik

# Create acme-dns.json for Traefik
echo "📝 Creating Traefik acme-dns configuration..."
cat > ./traefik/acme-dns.json <<EOF
{
  "*.${DOMAIN}": {
    "auth_url": "http://acme-dns:80",
    "subdomain": "${SUBDOMAIN}",
    "username": "${USERNAME}",
    "password": "${PASSWORD}"
  },
  "${DOMAIN}": {
    "auth_url": "http://acme-dns:80",
    "subdomain": "${SUBDOMAIN}",
    "username": "${USERNAME}",
    "password": "${PASSWORD}"
  }
}
EOF

echo "🎯 IMPORTANT: Add these DNS records to your GoDaddy domain management:"
echo ""
echo "Record 1 (Base domain):"
echo "Type: A"
echo "Name: auth.dev"
echo "Value: ${SERVER_IP}"
echo "TTL: 600"
echo ""
echo "Record 2 (CNAME for wildcard):"
echo "Type: CNAME"
echo "Name: _acme-challenge.dev"
echo "Value: ${FULLDOMAIN}"
echo "TTL: 600"
echo ""
echo "Record 3 (CNAME for base domain):"
echo "Type: CNAME"
echo "Name: _acme-challenge"
echo "Value: ${FULLDOMAIN}"
echo "TTL: 600"
echo ""
echo "🔑 Save these credentials securely:"
echo "Subdomain: ${SUBDOMAIN}"
echo "Username: ${USERNAME}"
echo "Password: ${PASSWORD}"
echo "Full Domain: ${FULLDOMAIN}"
echo ""
echo "⏳ After adding the DNS records, wait 5-10 minutes for propagation"
echo "🚀 Then run: docker compose up -d traefik"
echo "✨ Your wildcard SSL certificates will be automatically generated!"
echo ""
echo "🏗️  Architecture: acme-dns is now fully routed through Traefik!" 