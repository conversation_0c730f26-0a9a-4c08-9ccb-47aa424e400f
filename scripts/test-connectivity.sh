#!/bin/bash

# Connectivity Test Script
# Tests internal service connectivity and SSL setup

set -e

echo "🔗 Testing internal service connectivity..."

# Load environment variables
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    exit 1
fi

source .env

echo ""
echo "📋 Checking container status..."
docker compose ps

echo ""
echo "🌐 Testing internal connectivity..."

# Test acme-dns from Traefik
echo "Testing Traefik → acme-dns..."
if docker compose exec -T traefik curl -s http://acme-dns:80 > /dev/null; then
    echo "✅ Traefik can reach acme-dns"
else
    echo "❌ Traefik cannot reach acme-dns"
fi

# Test acme-dns external access (if DNS is configured)
echo "Testing external acme-dns access..."
if curl -s --connect-timeout 5 "http://auth.${DOMAIN}" > /dev/null 2>&1; then
    echo "✅ External access to acme-dns working"
else
    echo "⚠️  External access to acme-dns not available (DNS may not be configured yet)"
fi

echo ""
echo "🔍 Checking environment variables..."

# Check Traefik environment
echo "Testing ACME_DNS_API_BASE configuration..."
if docker compose exec -T traefik env | grep -q "ACME_DNS_API_BASE"; then
    echo "✅ ACME_DNS_API_BASE is set"
    docker compose exec -T traefik env | grep ACME_DNS_API_BASE
else
    echo "❌ ACME_DNS_API_BASE is missing"
fi

echo ""
echo "📁 Checking configuration files..."

# Check acme-dns.json exists
if [ -f "./traefik/acme-dns.json" ]; then
    echo "✅ acme-dns.json configuration exists"
    echo "   Domains configured: $(jq -r 'keys[]' ./traefik/acme-dns.json | tr '\n' ' ')"
else
    echo "❌ acme-dns.json configuration missing"
    echo "   Run: ./scripts/setup-acme-dns.sh"
fi

echo ""
echo "🔒 Testing SSL readiness..."

# Check for staging vs production
if docker compose exec -T traefik ps aux | grep -q "acme-staging"; then
    echo "🧪 Using STAGING certificates (good for testing)"
    echo "   Certificates will show 'Fake LE Intermediate'"
else
    echo "🚀 Using PRODUCTION certificates"
    echo "   Make sure DNS is properly configured first!"
fi

echo ""
echo "📊 Recent Traefik logs (last 10 lines):"
docker compose logs --tail=10 traefik | grep -E "(acme|certificate|error)" || echo "   No relevant logs found"

echo ""
echo "🎯 Next steps:"
echo "1. Ensure DNS records are configured in GoDaddy"
echo "2. Wait 5-10 minutes for DNS propagation"
echo "3. Monitor logs: docker compose logs -f traefik"
echo "4. Access services via https://service.${DOMAIN}" 