#!/bin/bash

# Copy certificates from server script
# This script helps copy certificates from your server VM to local directory

set -e

echo "📋 Copying SSL certificates from server..."

# Check if certificates directory exists
mkdir -p ./certs

echo "🔐 Copy your certificates from the server to ./certs/ directory:"
echo ""
echo "On your server VM, run:"
echo "  sudo cp /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem /tmp/"
echo "  sudo cp /etc/letsencrypt/live/dev.arumai.ai/privkey.pem /tmp/"
echo "  sudo chmod 644 /tmp/fullchain.pem /tmp/privkey.pem"
echo ""
echo "Then copy them to your local machine:"
echo "  scp user@*************:/tmp/fullchain.pem ./certs/"
echo "  scp user@*************:/tmp/privkey.pem ./certs/"
echo ""
echo "Or if you have the certificates locally already:"
echo "  cp /path/to/your/fullchain.pem ./certs/"
echo "  cp /path/to/your/privkey.pem ./certs/"
echo ""

# Check if certificates exist locally
if [ -f "./certs/fullchain.pem" ] && [ -f "./certs/privkey.pem" ]; then
    echo "✅ Certificates found in ./certs/"
    
    # Set proper permissions
    chmod 644 ./certs/fullchain.pem ./certs/privkey.pem
    
    echo "📝 Updating docker-compose.yml to use local certificates..."
    
    # Check if docker-compose needs updating
    if grep -q "/etc/letsencrypt/live/dev.arumai.ai" docker-compose.yml; then
        echo "⚠️  Updating volume mount in docker-compose.yml..."
        echo "   Changing from server path to local certs directory"
    fi
    
    echo ""
    echo "✅ Ready to deploy! Next steps:"
    echo "1. Deploy the updated docker-compose.yml to your server"
    echo "2. Copy the ./certs/ directory to your server"
    echo "3. Run: docker-compose up -d"
    
else
    echo "❌ Certificates not found in ./certs/"
    echo "Please copy them as instructed above, then run this script again."
    exit 1
fi 