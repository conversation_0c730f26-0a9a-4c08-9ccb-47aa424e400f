#!/bin/bash

# SSL Certificate Generation Script for dev.arumai.ai
# This script generates a wildcard SSL certificate using Let's Encrypt and GoDaddy DNS challenge

set -e

# Load environment variables
source .env

echo "🔒 Generating wildcard SSL certificate for *.dev.arumai.ai"

# Ensure certbot container exists and credentials are set
if [ ! -f "./certbot/godaddy-credentials.ini" ]; then
    echo "❌ Error: GoDaddy credentials file not found!"
    echo "Please create ./certbot/godaddy-credentials.ini with your GoDaddy API credentials"
    exit 1
fi

# Replace placeholders in credentials file
sed -i.bak "s/your_godaddy_api_key/${GODADDY_API_KEY}/g" ./certbot/godaddy-credentials.ini
sed -i.bak "s/your_godaddy_api_secret/${GODADDY_API_SECRET}/g" ./certbot/godaddy-credentials.ini

echo "📋 Setting up certbot permissions..."
chmod 600 ./certbot/godaddy-credentials.ini

echo "🚀 Running certbot to generate wildcard certificate..."

# Generate the wildcard certificate
docker run --rm \
    -v "$(pwd)/certbot/godaddy-credentials.ini:/etc/letsencrypt/godaddy-credentials.ini:ro" \
    -v "certbot_data:/etc/letsencrypt" \
    -v "certbot_logs:/var/log/letsencrypt" \
    certbot/dns-godaddy \
    certonly \
    --dns-godaddy \
    --dns-godaddy-credentials /etc/letsencrypt/godaddy-credentials.ini \
    --email "${EMAIL}" \
    --agree-tos \
    --no-eff-email \
    -d "*.dev.arumai.ai" \
    -d "dev.arumai.ai"

echo "✅ SSL certificate generated successfully!"
echo "📝 Certificate files:"
echo "   - Certificate: /etc/letsencrypt/live/dev.arumai.ai/fullchain.pem"
echo "   - Private Key: /etc/letsencrypt/live/dev.arumai.ai/privkey.pem"

echo "🔄 You can now start the nginx service with SSL enabled"
echo "Run: docker-compose up -d nginx" 