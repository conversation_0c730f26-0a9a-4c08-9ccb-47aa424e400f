# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Documentation
README.md
docs/
*.md

# Environment files
.env
.env.local
.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Certificates and keys
certs/
*.pem
*.key
*.crt

# Backup files
*.bak
*.backup

# Temporary files
tmp/
temp/

# Node modules (if any)
node_modules/

# Nginx config (not needed in container)
nginx/

# Traefik config (not needed in container)
traefik/

# Scripts (not needed in container)
scripts/

# Certbot (not needed in container)
certbot/

# ACME DNS (not needed in container)
acme-dns/
