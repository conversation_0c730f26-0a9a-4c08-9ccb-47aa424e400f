# Docker Setup for AI Agentic Workflows

This setup provides a containerized environment for your AI agentic workflows with Ollama, n8n, Flowise, and your custom Python application.

## Services Overview

- **ai-agentic-app**: Your custom Python FastAPI application
- **ollama**: LLM inference server (CPU/GPU variants)
- **n8n**: Workflow automation platform
- **flowise**: AI chatflow builder
- **postgres**: Database for n8n
- **qdrant**: Vector database
- **open-webui**: Web interface for Ollama
- **traefik**: Reverse proxy with SSL

## Quick Start

1. **Copy environment file:**
   ```bash
   cp env.example .env
   ```

2. **Edit the .env file with your configuration:**
   - Set your domain name
   - Configure passwords and secrets
   - Set database credentials

3. **Start the services:**
   ```bash
   # For CPU-only setup
   docker-compose --profile cpu up -d

   # For GPU setup (NVIDIA)
   docker-compose --profile gpu-nvidia up -d
   ```

4. **Access your services:**
   - Your Python app: `https://app.yourdomain.com`
   - Ollama API: `https://ai.yourdomain.com`
   - n8n: `https://n8n.yourdomain.com`
   - Flowise: `https://flowise.yourdomain.com`
   - Open WebUI: `https://chat.yourdomain.com`

## Your Python Application

The `ai-agentic-app` service runs your FastAPI application with the following features:

### API Endpoints

- `GET /` - Service information
- `GET /health` - Health check for all services
- `POST /chat` - Chat with Ollama LLM
- `GET /models` - List available Ollama models
- `POST /n8n/webhook/{workflow_id}` - Trigger n8n workflows

### Environment Variables

Your application has access to these environment variables:

- `OLLAMA_BASE_URL`: `http://ollama:11434/v1`
- `OLLAMA_API_URL`: `http://ollama:11434/v1/chat/completions`
- `POSTGRES_HOST`: `postgres`
- `N8N_URL`: `http://n8n:5678`

### Example Usage

```python
import requests

# Chat with Ollama
response = requests.post("http://localhost:8000/chat", json={
    "messages": [{"role": "user", "content": "Hello!"}],
    "model": "llama3.1"
})

# Check service health
health = requests.get("http://localhost:8000/health")
```

## Ollama Integration

Your application can communicate with Ollama using the internal Docker network:

- **Ollama API**: `http://ollama:11434/v1/chat/completions`
- **Models API**: `http://ollama:11434/api/tags`

### Available Models

The setup automatically pulls these models:
- `llama3.1` - Main chat model
- `nomic-embed-text` - Embedding model

## Development

### Building and Running Locally

```bash
# Build your application
docker-compose build ai-agentic-app

# Run just your application
docker-compose up ai-agentic-app

# View logs
docker-compose logs -f ai-agentic-app
```

### Adding Dependencies

Update `pyproject.toml` and rebuild:

```bash
# Add new dependency to pyproject.toml
# Then rebuild
docker-compose build ai-agentic-app
docker-compose up -d ai-agentic-app
```

### Volume Mounts

- `./shared:/app/shared` - Shared data between services
- `./tiny_agents:/app/tiny_agents` - Your agent configurations

## Troubleshooting

### Check Service Status

```bash
# Check all services
docker-compose ps

# Check specific service logs
docker-compose logs ai-agentic-app
docker-compose logs ollama
```

### Test Ollama Connection

```bash
# From inside your application container
docker-compose exec ai-agentic-app curl http://ollama:11434/api/tags

# Test chat endpoint
docker-compose exec ai-agentic-app curl -X POST http://ollama:11434/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model":"llama3.1","messages":[{"role":"user","content":"Hello"}]}'
```

### Common Issues

1. **Ollama not responding**: Wait for model download to complete
2. **Permission errors**: Check file ownership and Docker permissions
3. **Port conflicts**: Ensure ports 80, 443, 5432, 6333, 11434 are available

## Security Notes

- Change default passwords in `.env`
- Use strong encryption keys
- Consider firewall rules for production
- SSL certificates are handled by Traefik

## Scaling

To scale your application:

```bash
docker-compose up -d --scale ai-agentic-app=3
```

Note: You'll need to configure load balancing in Traefik for multiple instances.
