# SSL Setup with Wildcard Certificate and Nginx Reverse Proxy

This guide walks you through setting up wildcard SSL certificates with Let's Encrypt and nginx reverse proxy for your AI services on `dev.arumai.ai`.

## 🎯 Services and Subdomains

After setup, your services will be available at:

- **N8N Automation**: https://n8n.dev.arumai.ai
- **Flowise Chatbots**: https://flowise.dev.arumai.ai  
- **Open WebUI Chat**: https://chat.dev.arumai.ai
- **Qdrant Vector DB**: https://vector.dev.arumai.ai
- **Ollama AI Models**: https://ai.dev.arumai.ai

## 🚀 Prerequisites

1. **Ubuntu VPS Server** with Docker and Docker Compose installed
2. **Domain**: `dev.arumai.ai` managed by GoDaddy
3. **GoDaddy API Credentials** for DNS challenge
4. **DNS Records**: Wildcard A record `*.dev.arumai.ai` pointing to your server IP

## 📋 Step-by-Step Setup

### Step 1: Get GoDaddy API Credentials

1. Go to [GoDaddy Developer Portal](https://developer.godaddy.com/keys)
2. Create a new API key for Production environment
3. Note down your API Key and Secret

### Step 2: Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp env.example .env
   ```

2. Edit `.env` and update the following values:
   ```bash
   # Database Configuration
   POSTGRES_USER=arumai
   POSTGRES_PASSWORD=your_secure_password_here
   POSTGRES_DB=arumai_db

   # N8N Configuration  
   N8N_ENCRYPTION_KEY=your_n8n_encryption_key_here
   N8N_USER_MANAGEMENT_JWT_SECRET=your_jwt_secret_here

   # Flowise Configuration
   FLOWISE_USERNAME=admin
   FLOWISE_PASSWORD=your_flowise_password_here

   # Domain Configuration
   DOMAIN=dev.arumai.ai
   EMAIL=<EMAIL>

   # GoDaddy API for DNS Challenge
   GODADDY_API_KEY=your_actual_godaddy_api_key
   GODADDY_API_SECRET=your_actual_godaddy_api_secret
   ```

### Step 3: Set Up DNS Records

Add these DNS records in your GoDaddy domain management:

```
Type: A
Name: *.dev
Value: YOUR_SERVER_IP
TTL: 600

Type: A  
Name: dev
Value: YOUR_SERVER_IP
TTL: 600
```

### Step 4: Update GoDaddy Credentials

Edit `certbot/godaddy-credentials.ini` with your actual API credentials:
```ini
dns_godaddy_key = your_actual_godaddy_api_key
dns_godaddy_secret = your_actual_godaddy_api_secret
```

### Step 5: Generate SSL Certificate

Run the SSL generation script:
```bash
./scripts/generate-ssl.sh
```

This will:
- Generate a wildcard SSL certificate for `*.dev.arumai.ai`
- Use GoDaddy DNS challenge for verification
- Store certificates in Docker volumes

### Step 6: Start Services

1. Start the core services first:
   ```bash
   # For CPU-only setup
   docker-compose --profile cpu up -d postgres flowise open-webui n8n qdrant ollama-cpu

   # For GPU setup (if you have NVIDIA GPU)
   docker-compose --profile gpu-nvidia up -d postgres flowise open-webui n8n qdrant ollama-gpu
   ```

2. Start nginx reverse proxy:
   ```bash
   docker-compose up -d nginx
   ```

3. Start certbot for automatic certificate renewal:
   ```bash
   docker-compose up -d certbot
   ```

## 🔧 Troubleshooting

### Certificate Generation Issues

1. **GoDaddy API Error**: Verify your API credentials are correct
2. **DNS Propagation**: Wait 5-10 minutes after creating DNS records
3. **Rate Limits**: Let's Encrypt has rate limits; wait if you hit them

### Nginx Configuration Issues

1. **502 Bad Gateway**: Check if backend services are running
2. **SSL Issues**: Verify certificate files exist in the certbot volume
3. **Permission Issues**: Ensure nginx can read certificate files

### Check Service Status

```bash
# Check all services
docker-compose ps

# Check nginx logs
docker-compose logs nginx

# Check certbot logs  
docker-compose logs certbot

# Check specific service logs
docker-compose logs flowise
```

### Test SSL Certificate

```bash
# Test certificate
openssl s_client -connect n8n.dev.arumai.ai:443 -servername n8n.dev.arumai.ai

# Check certificate expiry
echo | openssl s_client -connect n8n.dev.arumai.ai:443 2>/dev/null | openssl x509 -noout -dates
```

## 🔄 Certificate Renewal

The setup includes automatic certificate renewal via the certbot container, which:
- Runs every 12 hours
- Automatically renews certificates when they're within 30 days of expiry
- Uses the same GoDaddy DNS challenge method

## 🛡️ Security Features

- **HTTPS Redirect**: All HTTP traffic redirected to HTTPS
- **Security Headers**: XSS protection, HSTS, frame denial
- **TLS 1.2/1.3**: Modern encryption protocols only
- **Strong Ciphers**: ECDHE ciphers with perfect forward secrecy

## 📊 Monitoring

Monitor your setup with:

```bash
# Check certificate expiry dates
docker exec certbot certbot certificates

# View nginx access logs
docker-compose logs nginx | tail -f

# Check service health
curl -I https://n8n.dev.arumai.ai
```

## 🎉 Success!

If everything is working correctly, you should be able to access:

- ✅ https://n8n.dev.arumai.ai (N8N automation platform)
- ✅ https://flowise.dev.arumai.ai (Flowise chatbot builder)  
- ✅ https://chat.dev.arumai.ai (Open WebUI for AI chat)
- ✅ https://vector.dev.arumai.ai (Qdrant vector database)
- ✅ https://ai.dev.arumai.ai (Ollama AI models)

All with valid wildcard SSL certificates! 🎊 