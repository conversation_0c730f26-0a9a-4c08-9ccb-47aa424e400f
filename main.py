"""
Main FastAPI application for AI Agentic Workflows
"""

import os
import requests
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import json

app = FastAPI(
    title="AI Agentic Workflows",
    description="AI workflow orchestration with n8n, Ollama, and other services",
    version="0.1.0"
)

# Configuration
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://ollama:11434/v1")
OLLAMA_API_URL = os.getenv("OLLAMA_API_URL", "http://ollama:11434/v1/chat/completions")
N8N_URL = os.getenv("N8N_URL", "http://n8n:5678")

class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    model: str = "llama3.1"
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 1000

class ChatResponse(BaseModel):
    response: str
    model: str
    usage: Optional[Dict[str, Any]] = None

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "AI Agentic Workflows API",
        "version": "0.1.0",
        "services": {
            "ollama": OLLAMA_BASE_URL,
            "n8n": N8N_URL
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    services_status = {}
    
    # Check Ollama
    try:
        response = requests.get(f"{OLLAMA_BASE_URL.replace('/v1', '')}/api/tags", timeout=5)
        services_status["ollama"] = "healthy" if response.status_code == 200 else "unhealthy"
    except Exception as e:
        services_status["ollama"] = f"unhealthy: {str(e)}"
    
    # Check N8N
    try:
        response = requests.get(f"{N8N_URL}/healthz", timeout=5)
        services_status["n8n"] = "healthy" if response.status_code == 200 else "unhealthy"
    except Exception as e:
        services_status["n8n"] = f"unhealthy: {str(e)}"
    
    return {
        "status": "healthy",
        "services": services_status
    }

@app.post("/chat", response_model=ChatResponse)
async def chat_with_ollama(request: ChatRequest):
    """Chat with Ollama LLM"""
    try:
        # Prepare the request for Ollama
        ollama_request = {
            "model": request.model,
            "messages": [{"role": msg.role, "content": msg.content} for msg in request.messages],
            "temperature": request.temperature,
            "max_tokens": request.max_tokens,
            "stream": False
        }
        
        # Make request to Ollama
        response = requests.post(
            OLLAMA_API_URL,
            json=ollama_request,
            headers={"Content-Type": "application/json"},
            timeout=60
        )
        
        if response.status_code != 200:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Ollama API error: {response.text}"
            )
        
        ollama_response = response.json()
        
        return ChatResponse(
            response=ollama_response["choices"][0]["message"]["content"],
            model=request.model,
            usage=ollama_response.get("usage")
        )
        
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to Ollama: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@app.get("/models")
async def list_models():
    """List available models from Ollama"""
    try:
        response = requests.get(f"{OLLAMA_BASE_URL.replace('/v1', '')}/api/tags", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail="Failed to fetch models from Ollama"
            )
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to Ollama: {str(e)}"
        )

@app.post("/n8n/webhook/{workflow_id}")
async def trigger_n8n_workflow(workflow_id: str, payload: Dict[str, Any]):
    """Trigger an n8n workflow"""
    try:
        webhook_url = f"{N8N_URL}/webhook/{workflow_id}"
        response = requests.post(
            webhook_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"n8n workflow error: {response.text}"
            )
            
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=503,
            detail=f"Failed to connect to n8n: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
